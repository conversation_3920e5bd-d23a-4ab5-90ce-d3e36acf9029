# Code Llama 7B Multi-Language Fine-tuning Project

A comprehensive project for fine-tuning Code Llama 7B on AWS with multi-language support and on-device optimization for web development, systems programming, mobile development, and game engines.

## 🎯 Project Overview

This project enables you to:
- **Train** Code Llama 7B on cost-optimized AWS infrastructure
- **Support** multiple programming languages and frameworks
- **Deploy** optimized models on mobile and edge devices
- **Monitor** training costs and performance

### Supported Languages & Frameworks

#### Web Development
- **Backend**: Python (Django, FastAPI, Flask), PHP (Laravel, Symfony), Java (Spring Boot)
- **Frontend**: JavaScript/TypeScript (React, Vue, Angular), HTML/CSS
- **Full-stack**: Node.js, Next.js, Nuxt.js

#### Systems Programming
- **Languages**: C, C++, Rust, Go
- **Scripting**: Bash, PowerShell
- **Assembly**: x86, ARM

#### Mobile Development
- **Native**: <PERSON><PERSON><PERSON> (Android), Swift (iOS)
- **Cross-platform**: Flutter (Dart), React Native (JavaScript)
- **APIs**: Django REST, Falcon API

#### Game Development
- **Unity**: C#
- **Godot**: GDScript, C#
- **Unreal**: C++, Blueprint

## 📁 Project Structure

```
├── dataset_collector.py          # Automated dataset collection (50K samples)
├── train_code_llama.py          # Main training script with LoRA + 4-bit
├── training_config.yaml         # Training configuration
├── ec2_quick_setup.sh           # Complete EC2 environment setup
├── activate_env.sh              # Environment activation script
├── set_env_vars.sh              # API keys configuration
├── start_services.sh            # Start Jupyter + TensorBoard
├── model_accuracy_and_download.md # Model info and accuracy comparison
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## 🚀 Quick Start

### 1. Prerequisites

- AWS Account with appropriate permissions
- GitHub Personal Access Token
- Python 3.10+
- CUDA-compatible GPU (for local development)

### 2. Environment Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd code-llama-training

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export GITHUB_TOKEN="your_github_token"
export WANDB_API_KEY="your_wandb_key"
export AWS_ACCESS_KEY_ID="your_aws_key"
export AWS_SECRET_ACCESS_KEY="your_aws_secret"
```

### 3. AWS Infrastructure Setup

```bash
# Make setup script executable
chmod +x setup_aws_training.sh

# Configure your AWS settings in the script
vim setup_aws_training.sh

# Launch AWS training environment
./setup_aws_training.sh
```

### 4. Dataset Collection

```bash
# Collect exactly 50K training samples from GitHub
python dataset_collector.py

# This will create:
# - ./dataset/raw_data/        # Repository metadata
# - ./dataset/processed/       # 50K samples (train/val/test splits)
# - ./dataset/dataset_statistics.json  # Dataset statistics
```

### 5. Model Training

```bash
# SSH to your AWS instance
ssh -i your-key.pem ubuntu@your-instance-ip

# Navigate to training directory
cd /data/codellama-training

# Start training with default configuration
python train_code_llama.py \
    --output_dir ./models/codellama-7b-finetuned \
    --num_train_epochs 3 \
    --per_device_train_batch_size 4 \
    --learning_rate 2e-4 \
    --report_to wandb

# Or use configuration file
python train_code_llama.py --config training_config.yaml
```

## 💰 Cost Optimization

### AWS Cost Estimates

| Instance Type | On-Demand | Spot Price | Training Time | Total Cost |
|---------------|-----------|------------|---------------|------------|
| g4dn.xlarge   | $0.526/hr | $0.158/hr  | 6-10 hours    | $0.95-$1.58 |
| g4dn.2xlarge  | $0.752/hr | $0.226/hr  | 4-6 hours     | $0.90-$1.36 |
| p3.2xlarge    | $3.06/hr  | $0.918/hr  | 3-5 hours     | $2.75-$4.59 |

### Cost Monitoring

```bash
# Monitor current costs
bash monitor-costs.sh

# Set up cost alerts
aws budgets create-budget --account-id YOUR_ACCOUNT_ID --budget file://budget.json
```

## 📊 Training Configuration

### Default Settings (Optimized for g4dn.xlarge)

```yaml
# Memory-efficient training
per_device_train_batch_size: 4
gradient_accumulation_steps: 4
fp16: true
gradient_checkpointing: true

# LoRA configuration
lora_r: 16
lora_alpha: 32
lora_dropout: 0.1

# 4-bit quantization
use_4bit: true
bnb_4bit_compute_dtype: "float16"
bnb_4bit_quant_type: "nf4"
```

### Custom Configuration

Edit `training_config.yaml` to customize:
- Learning rate and schedule
- Batch sizes and accumulation
- LoRA parameters
- Quantization settings
- Monitoring and logging

## 📱 On-Device Deployment

### Model Optimization Pipeline

```python
from model_optimization import ModelOptimizationPipeline

# Create optimization pipeline
pipeline = ModelOptimizationPipeline("./models/codellama-7b-finetuned", "mobile")

# Add optimization steps
pipeline.add_quantization("dynamic", 8)
pipeline.add_pruning(0.2, "structured")

# Execute pipeline
optimized_model = pipeline.execute_pipeline("./optimized_models/")
```

### Platform-Specific Deployment

#### iOS (Core ML)
```bash
python -c "
from on_device_optimization import convert_to_coreml
convert_to_coreml('./models/codellama-7b-finetuned', './ios/CodeLlama.mlmodel')
"
```

#### Android (TensorFlow Lite)
```bash
python -c "
from on_device_optimization import convert_to_tflite
convert_to_tflite('./models/codellama-7b-finetuned', './android/codellama.tflite')
"
```

## 📈 Monitoring and Evaluation

### Training Monitoring

- **Weights & Biases**: Real-time training metrics
- **TensorBoard**: Loss visualization and model graphs
- **AWS CloudWatch**: Instance monitoring and cost tracking

### Model Evaluation

```bash
# Evaluate on test set
python evaluate_model.py \
    --model_path ./models/codellama-7b-finetuned \
    --test_data ./dataset/processed/test.json

# Benchmark inference performance
python benchmark_inference.py \
    --model_path ./models/codellama-7b-finetuned \
    --num_samples 100
```

## 🔧 Troubleshooting

### Common Issues

1. **Out of Memory Errors**
   - Reduce batch size in `training_config.yaml`
   - Enable gradient checkpointing
   - Use smaller LoRA rank (r=8)

2. **Slow Training**
   - Increase batch size if memory allows
   - Use larger AWS instance
   - Enable mixed precision training

3. **High AWS Costs**
   - Use spot instances
   - Monitor with cost alerts
   - Stop instances when not training

### Debug Commands

```bash
# Check GPU memory usage
nvidia-smi

# Monitor training progress
tail -f logs/training.log

# Check AWS costs
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31
```

## 📚 Documentation

- [AWS Training Approach](aws_training_approach.md) - Detailed AWS setup guide
- [Dataset Creation Guide](dataset_creation_guide.md) - Data collection methodology
- [On-Device Optimization](on_device_optimization.md) - Mobile deployment guide

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Meta AI for Code Llama
- Hugging Face for Transformers library
- AWS for cloud infrastructure
- The open-source community for datasets and tools

## 📞 Support

For questions and support:
- Create an issue in this repository
- Check the troubleshooting section
- Review the documentation files

---

**Happy coding! 🚀**
