#!/bin/bash
# Start Monitoring Services
# Starts Jupyter Notebook and TensorBoard for remote monitoring

cd /data/codellama-training

# Activate environment
source $(conda info --base)/etc/profile.d/conda.sh
conda activate llama-training

# Kill any existing services
echo "Stopping existing services..."
pkill -f tensorboard
pkill -f jupyter
sleep 2

# Create logs directory
mkdir -p logs

# Start TensorBoard
echo "Starting TensorBoard..."
nohup tensorboard --logdir=./logs --host=0.0.0.0 --port=6006 --reload_interval=30 > tensorboard.log 2>&1 &
TENSORBOARD_PID=$!

# Start Jupyter Notebook
echo "Starting Jupyter Notebook..."
nohup jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root --notebook-dir=/data/codellama-training > jupyter.log 2>&1 &
JUPYTER_PID=$!

# Wait a moment for services to start
sleep 5

# Get public IP
PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "localhost")

# Display access information
echo ""
echo "=== Services Started ==="
echo "TensorBoard PID: $TENSORBOARD_PID"
echo "Jupyter PID: $JUPYTER_PID"
echo ""
echo "=== Access URLs ==="
echo "TensorBoard: http://$PUBLIC_IP:6006"
echo "Jupyter Notebook: http://$PUBLIC_IP:8888"
echo ""
echo "=== Log Files ==="
echo "TensorBoard logs: tensorboard.log"
echo "Jupyter logs: jupyter.log"
echo ""
echo "=== Useful Commands ==="
echo "View TensorBoard logs: tail -f tensorboard.log"
echo "View Jupyter logs: tail -f jupyter.log"
echo "Stop services: pkill -f tensorboard && pkill -f jupyter"
echo "Check processes: ps aux | grep -E '(tensorboard|jupyter)'"

# Check if services are running
sleep 2
if pgrep -f tensorboard > /dev/null; then
    echo "✓ TensorBoard is running"
else
    echo "✗ TensorBoard failed to start - check tensorboard.log"
fi

if pgrep -f jupyter > /dev/null; then
    echo "✓ Jupyter is running"
else
    echo "✗ Jupyter failed to start - check jupyter.log"
fi
