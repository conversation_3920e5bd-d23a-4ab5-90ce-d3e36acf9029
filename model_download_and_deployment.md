# Model Download and Deployment Guide

## 📍 Where Your Trained Model Will Be Located

### On AWS EC2 Instance
After training completes, your model will be saved in:

```
/data/codellama-training/models/codellama-7b-finetuned/
├── adapter_config.json          # LoRA configuration
├── adapter_model.safetensors    # LoRA weights (~100-200MB)
├── README.md                    # Model card
├── tokenizer.json              # Tokenizer files
├── tokenizer_config.json
├── special_tokens_map.json
└── training_args.bin           # Training arguments
```

**Important**: With LoRA fine-tuning, you only get the **adapter weights** (~100-200MB), not the full model. You have two deployment options:

### Option 1: Runtime Combination (Recommended)
- **Base Model**: ~13.5GB (downloaded once)
- **LoRA Adapters**: ~100-200MB (your trained weights)
- **Memory Usage**: ~4.2GB (with 4-bit quantization)
- **Combines at runtime** - more flexible

### Option 2: Merge into Single Model
- **Merged Model**: ~13.7GB (base + adapters combined)
- **Memory Usage**: ~4.2GB (with 4-bit quantization)
- **Single file** - easier to distribute

## 📥 Download Methods from AWS to MacBook

### Method 1: SCP (Recommended)
```bash
# From your MacBook terminal
# Download the entire model directory
scp -i your-key.pem -r ubuntu@YOUR_EC2_IP:/data/codellama-training/models/codellama-7b-finetuned ./

# Or download specific files
scp -i your-key.pem ubuntu@YOUR_EC2_IP:/data/codellama-training/models/codellama-7b-finetuned/adapter_model.safetensors ./
scp -i your-key.pem ubuntu@YOUR_EC2_IP:/data/codellama-training/models/codellama-7b-finetuned/adapter_config.json ./
```

### Method 2: AWS S3 (Recommended for Backup)
```bash
# On EC2 instance - upload to S3
aws s3 sync /data/codellama-training/models/codellama-7b-finetuned/ s3://your-bucket/models/codellama-7b-finetuned/

# On MacBook - download from S3
aws s3 sync s3://your-bucket/models/codellama-7b-finetuned/ ./codellama-7b-finetuned/
```

### Method 3: Hugging Face Hub (Best for Sharing)
```bash
# On EC2 instance - upload to Hugging Face
pip install huggingface_hub
python -c "
from huggingface_hub import HfApi
api = HfApi()
api.upload_folder(
    folder_path='/data/codellama-training/models/codellama-7b-finetuned',
    repo_id='your-username/codellama-7b-finetuned',
    repo_type='model'
)
"

# On MacBook - download from Hugging Face
git lfs install
git clone https://huggingface.co/your-username/codellama-7b-finetuned
```

### Method 4: Rsync (For Large Files)
```bash
# From your MacBook
rsync -avz -e "ssh -i your-key.pem" ubuntu@YOUR_EC2_IP:/data/codellama-training/models/codellama-7b-finetuned/ ./codellama-7b-finetuned/
```

## 🚀 Deployment on MacBook M2

You have **two deployment approaches**:

### Approach 1: Runtime Combination (Recommended)
**Pros**: Flexible, can swap adapters, smaller storage
**Cons**: Slightly slower loading

### Approach 2: Merged Model
**Pros**: Faster loading, single file, easier distribution
**Cons**: Larger storage, less flexible

### Step 1: Setup Local Environment
```bash
# Create conda environment on MacBook
conda create -n codellama-local python=3.10 -y
conda activate codellama-local

# Install dependencies
pip install torch transformers peft accelerate bitsandbytes
pip install gradio streamlit  # For web interface
```

### Step 2A: Runtime Combination Deployment (Recommended)
```python
# save as: deploy_model_runtime.py
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
import time

class CodeLlamaRuntimeDeployment:
    def __init__(self, base_model_path="codellama/CodeLlama-7b-hf", 
                 adapter_path="./codellama-7b-finetuned"):
        self.device = "mps" if torch.backends.mps.is_available() else "cpu"
        print(f"Using device: {self.device}")
        
        # Load base model with 4-bit quantization
        print("Loading base model...")
        self.model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            device_map="auto",
            torch_dtype=torch.float16,
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            low_cpu_mem_usage=True
        )
        
        # Load LoRA adapter
        print("Loading LoRA adapter...")
        self.model = PeftModel.from_pretrained(self.model, adapter_path)
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(base_model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        print("Model loaded successfully!")
        self.print_memory_usage()
    
    def print_memory_usage(self):
        """Print current memory usage"""
        import psutil
        memory = psutil.virtual_memory()
        print(f"Memory usage: {memory.used/1e9:.1f}GB / {memory.total/1e9:.1f}GB")
        print(f"Available: {memory.available/1e9:.1f}GB")
    
    def generate_code(self, prompt, max_length=512, temperature=0.7):
        """Generate code from prompt"""
        # Format prompt
        formatted_prompt = f"### Instruction:\n{prompt}\n\n### Response:\n"
        
        # Tokenize
        inputs = self.tokenizer(
            formatted_prompt,
            return_tensors="pt",
            truncation=True,
            max_length=1024
        )
        
        # Generate
        start_time = time.time()
        with torch.no_grad():
            outputs = self.model.generate(
                inputs.input_ids,
                max_length=inputs.input_ids.shape[1] + max_length,
                temperature=temperature,
                do_sample=True,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                use_cache=True
            )
        
        # Decode response
        response = self.tokenizer.decode(
            outputs[0][inputs.input_ids.shape[1]:],
            skip_special_tokens=True
        )
        
        generation_time = time.time() - start_time
        tokens_generated = len(outputs[0]) - len(inputs.input_ids[0])
        
        print(f"Generated {tokens_generated} tokens in {generation_time:.2f}s")
        print(f"Speed: {tokens_generated/generation_time:.1f} tokens/second")
        
        return response.strip()

# Usage example
if __name__ == "__main__":
    # Initialize model
    llama = CodeLlamaRuntimeDeployment(
        base_model_path="codellama/CodeLlama-7b-hf",
        adapter_path="./codellama-7b-finetuned"
    )

    # Test generation
    prompt = "Write a Python function to calculate the factorial of a number"
    response = llama.generate_code(prompt)
    print(f"\nPrompt: {prompt}")
    print(f"Response:\n{response}")
```

### Step 2B: Merged Model Deployment
```python
# save as: merge_and_deploy.py
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
import time

def merge_lora_adapter(base_model_path, adapter_path, output_path):
    """Merge LoRA adapter with base model and save as single model"""
    print("Loading base model...")
    model = AutoModelForCausalLM.from_pretrained(
        base_model_path,
        torch_dtype=torch.float16,
        device_map="auto"
    )

    print("Loading LoRA adapter...")
    model = PeftModel.from_pretrained(model, adapter_path)

    print("Merging adapter with base model...")
    model = model.merge_and_unload()

    print(f"Saving merged model to {output_path}...")
    model.save_pretrained(output_path)

    # Also save tokenizer
    tokenizer = AutoTokenizer.from_pretrained(base_model_path)
    tokenizer.save_pretrained(output_path)

    print("Merge completed!")
    return output_path

class CodeLlamaMergedDeployment:
    def __init__(self, merged_model_path):
        self.device = "mps" if torch.backends.mps.is_available() else "cpu"
        print(f"Using device: {self.device}")

        # Load merged model with 4-bit quantization
        print("Loading merged model...")
        self.model = AutoModelForCausalLM.from_pretrained(
            merged_model_path,
            device_map="auto",
            torch_dtype=torch.float16,
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            low_cpu_mem_usage=True
        )

        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(merged_model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        print("Merged model loaded successfully!")
        self.print_memory_usage()

    def print_memory_usage(self):
        """Print current memory usage"""
        import psutil
        memory = psutil.virtual_memory()
        print(f"Memory usage: {memory.used/1e9:.1f}GB / {memory.total/1e9:.1f}GB")
        print(f"Available: {memory.available/1e9:.1f}GB")

    def generate_code(self, prompt, max_length=512, temperature=0.7):
        """Generate code from prompt"""
        # Format prompt
        formatted_prompt = f"### Instruction:\n{prompt}\n\n### Response:\n"

        # Tokenize
        inputs = self.tokenizer(
            formatted_prompt,
            return_tensors="pt",
            truncation=True,
            max_length=1024
        )

        # Generate
        start_time = time.time()
        with torch.no_grad():
            outputs = self.model.generate(
                inputs.input_ids,
                max_length=inputs.input_ids.shape[1] + max_length,
                temperature=temperature,
                do_sample=True,
                top_p=0.9,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                use_cache=True
            )

        # Decode response
        response = self.tokenizer.decode(
            outputs[0][inputs.input_ids.shape[1]:],
            skip_special_tokens=True
        )

        generation_time = time.time() - start_time
        tokens_generated = len(outputs[0]) - len(inputs.input_ids[0])

        print(f"Generated {tokens_generated} tokens in {generation_time:.2f}s")
        print(f"Speed: {tokens_generated/generation_time:.1f} tokens/second")

        return response.strip()

# Usage example
if __name__ == "__main__":
    # Option 1: Merge adapter first (run once)
    merged_path = "./codellama-7b-merged"
    merge_lora_adapter(
        base_model_path="codellama/CodeLlama-7b-hf",
        adapter_path="./codellama-7b-finetuned",
        output_path=merged_path
    )

    # Option 2: Use merged model
    llama = CodeLlamaMergedDeployment(merged_path)

    # Test generation
    prompt = "Write a Python function to calculate the factorial of a number"
    response = llama.generate_code(prompt)
    print(f"\nPrompt: {prompt}")
    print(f"Response:\n{response}")
```

### Step 3: Create Web Interface (Optional)
```python
# save as: web_interface.py
import gradio as gr
from deploy_model import CodeLlamaDeployment

# Initialize model (this will take a few minutes)
print("Loading model...")
llama = CodeLlamaDeployment()

def generate_code_interface(prompt, max_length, temperature):
    """Gradio interface function"""
    try:
        response = llama.generate_code(
            prompt=prompt,
            max_length=int(max_length),
            temperature=float(temperature)
        )
        return response
    except Exception as e:
        return f"Error: {str(e)}"

# Create Gradio interface
interface = gr.Interface(
    fn=generate_code_interface,
    inputs=[
        gr.Textbox(
            label="Code Prompt",
            placeholder="Write a Python function to...",
            lines=3
        ),
        gr.Slider(
            minimum=50,
            maximum=1000,
            value=300,
            label="Max Length"
        ),
        gr.Slider(
            minimum=0.1,
            maximum=1.0,
            value=0.7,
            label="Temperature"
        )
    ],
    outputs=gr.Textbox(
        label="Generated Code",
        lines=15
    ),
    title="Code Llama 7B - Fine-tuned",
    description="Generate code using your fine-tuned Code Llama model"
)

if __name__ == "__main__":
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False
    )
```

### Step 4: Command Line Interface
```python
# save as: cli_interface.py
import argparse
from deploy_model import CodeLlamaDeployment

def main():
    parser = argparse.ArgumentParser(description="Code Llama CLI")
    parser.add_argument("--prompt", type=str, required=True, help="Code generation prompt")
    parser.add_argument("--max-length", type=int, default=300, help="Maximum generation length")
    parser.add_argument("--temperature", type=float, default=0.7, help="Generation temperature")
    parser.add_argument("--adapter-path", type=str, default="./codellama-7b-finetuned", help="Path to LoRA adapter")
    
    args = parser.parse_args()
    
    # Initialize model
    llama = CodeLlamaDeployment(adapter_path=args.adapter_path)
    
    # Generate code
    response = llama.generate_code(
        prompt=args.prompt,
        max_length=args.max_length,
        temperature=args.temperature
    )
    
    print(f"\nPrompt: {args.prompt}")
    print(f"Generated Code:\n{response}")

if __name__ == "__main__":
    main()
```

## 🔧 Deployment Approach Comparison

### Runtime Combination vs Merged Model

| Aspect | Runtime Combination | Merged Model |
|--------|-------------------|--------------|
| **Storage** | Base: 13.5GB + Adapter: 0.2GB | Single: 13.7GB |
| **Loading Time** | 3-5 minutes | 2-3 minutes |
| **Memory Usage** | ~4.2GB | ~4.2GB |
| **Flexibility** | Can swap adapters | Fixed model |
| **Distribution** | Need base + adapter | Single file |
| **Recommended For** | Development, multiple adapters | Production, single model |

### Which Approach to Choose?

#### Choose **Runtime Combination** if:
- You want to experiment with different adapters
- You plan to train multiple specialized models
- You want to keep base model separate
- Storage space is not critical

#### Choose **Merged Model** if:
- You want faster loading
- You need to distribute the model
- You have a single, final model
- You want simpler deployment

## 🔧 Deployment Options

### Option 1: Runtime Combination
```bash
# Run the runtime deployment script
python deploy_model_runtime.py
```

### Option 2: Merged Model
```bash
# First, merge the adapter (run once)
python merge_and_deploy.py

# Then use the merged model
python deploy_merged_model.py
```

### Option 2: Web Interface
```bash
# Start web interface
python web_interface.py

# Access at: http://localhost:7860
```

### Option 3: Command Line Interface
```bash
# Generate code via CLI
python cli_interface.py --prompt "Write a Python function to sort a list" --max-length 200
```

### Option 4: Jupyter Notebook
```python
# In Jupyter notebook
from deploy_model import CodeLlamaDeployment

llama = CodeLlamaDeployment()
response = llama.generate_code("Write a JavaScript function to reverse a string")
print(response)
```

## 📊 Performance Expectations on M2 MacBook

### Memory Usage
- **Model Loading**: ~4.2GB RAM
- **During Generation**: +0.5GB (temporary)
- **Total Peak**: ~4.7GB RAM
- **Available for macOS**: ~11.3GB

### Speed Benchmarks
- **Model Loading Time**: 2-5 minutes (first time)
- **Generation Speed**: 8-12 tokens/second
- **Response Time**: 5-8 seconds for 50 tokens
- **Quality**: 95-98% of original model

## 🛠 Troubleshooting

### Common Issues and Solutions

#### 1. Out of Memory Error
```python
# Reduce memory usage
model = AutoModelForCausalLM.from_pretrained(
    base_model_path,
    device_map="auto",
    torch_dtype=torch.float16,
    load_in_8bit=True,  # Use 8-bit instead of 4-bit
    low_cpu_mem_usage=True
)
```

#### 2. Slow Generation
```python
# Optimize generation settings
outputs = model.generate(
    inputs.input_ids,
    max_length=200,  # Reduce max length
    do_sample=False,  # Use greedy decoding
    use_cache=True,   # Enable KV cache
    pad_token_id=tokenizer.eos_token_id
)
```

#### 3. Model Not Found
```bash
# Verify adapter files exist
ls -la ./codellama-7b-finetuned/
# Should show: adapter_config.json, adapter_model.safetensors

# Re-download if missing
scp -i your-key.pem -r ubuntu@YOUR_EC2_IP:/data/codellama-training/models/codellama-7b-finetuned ./
```

## 📋 Complete Deployment Checklist

### Before Downloading
- [ ] Training completed successfully on EC2
- [ ] Model files exist in `/data/codellama-training/models/codellama-7b-finetuned/`
- [ ] Backup model to S3 (recommended)

### Download Process
- [ ] Choose download method (SCP recommended)
- [ ] Download adapter files to MacBook
- [ ] Verify file integrity

### Local Setup
- [ ] Create conda environment
- [ ] Install dependencies
- [ ] Create deployment scripts
- [ ] Test model loading

### Deployment
- [ ] Choose interface (CLI, web, or script)
- [ ] Test generation
- [ ] Monitor memory usage
- [ ] Optimize performance if needed

Your fine-tuned Code Llama model will be ready to use on your MacBook with excellent performance! 🚀
