# Manual EC2 Setup Plan for Code Llama Training

## Overview
This plan guides you through manually creating an EC2 instance via AWS Console and setting up the complete training environment on the instance itself.

## Phase 1: EC2 Instance Creation (AWS Console)

### Step 1: Launch Instance
1. **Go to AWS Console** → EC2 → Launch Instance
2. **Name**: `codellama-training-instance`

### Step 2: Choose AMI
**Recommended**: Deep Learning AMI (Ubuntu 20.04)
- Search for: "Deep Learning AMI (Ubuntu 20.04)"
- AMI ID: `ami-0c02fb55956c7d316` (us-east-1)
- **Benefits**: Pre-installed CUDA, PyTorch, Transformers

**Alternative**: Ubuntu Server 20.04 LTS
- AMI ID: `ami-0a634ae95e11c6f91` (us-east-1)
- **Benefits**: Lower cost, full control

### Step 3: Instance Type Selection
**Primary Choice**: `g4dn.xlarge`
- 1x NVIDIA T4 (16GB VRAM)
- 4 vCPUs, 16 GB RAM
- Cost: ~$0.526/hr (On-Demand), ~$0.158/hr (Spot)

**Alternative**: `g4dn.2xlarge` (for faster training)
- 1x NVIDIA T4 (16GB VRAM)
- 8 vCPUs, 32 GB RAM
- Cost: ~$0.752/hr (On-Demand), ~$0.226/hr (Spot)

### Step 4: Key Pair
- **Create new key pair** or use existing
- **Name**: `codellama-training-key`
- **Type**: RSA
- **Format**: .pem
- **Download and save** the .pem file securely

### Step 5: Network Settings
- **VPC**: Default VPC
- **Subnet**: Default subnet (public)
- **Auto-assign public IP**: Enable
- **Security Group**: Create new
  - **Name**: `codellama-training-sg`
  - **Rules**:
    - SSH (22) - Your IP
    - Custom TCP (8888) - Your IP (Jupyter)
    - Custom TCP (6006) - Your IP (TensorBoard)

### Step 6: Storage Configuration
- **Root Volume**: 50 GB gp3
- **Add Volume**: 500 GB gp3 (for datasets and models)
  - Device: `/dev/sdf`
  - Volume Type: gp3
  - Size: 500 GB
  - Delete on termination: Yes

### Step 7: Advanced Details
- **IAM Instance Profile**: Create if needed for S3 access
- **User Data**: Leave empty (we'll setup manually)
- **Spot Instance**: Check "Request Spot Instances" for cost savings

### Step 8: Launch
- Review settings and click "Launch Instance"
- Wait for instance to be in "Running" state
- Note down the **Public IPv4 address**

## Phase 2: Initial EC2 Setup

### Step 1: Connect to Instance
```bash
# From your local machine
chmod 400 codellama-training-key.pem
ssh -i codellama-training-key.pem ubuntu@YOUR_PUBLIC_IP
```

### Step 2: System Update and Basic Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install essential tools
sudo apt install -y git wget curl htop nvtop screen tmux tree unzip

# Check GPU (should show T4)
nvidia-smi
```

### Step 3: Mount Additional Storage
```bash
# Check available disks
lsblk

# Format and mount the 500GB volume
sudo mkfs.ext4 /dev/nvme1n1
sudo mkdir -p /data
sudo mount /dev/nvme1n1 /data

# Make mount permanent
echo '/dev/nvme1n1 /data ext4 defaults 0 0' | sudo tee -a /etc/fstab

# Change ownership to ubuntu user
sudo chown -R ubuntu:ubuntu /data

# Verify mount
df -h
```

## Phase 3: Environment Setup

### Step 1: Install Miniconda (if not using Deep Learning AMI)
```bash
cd /home/<USER>
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -b -p /home/<USER>/miniconda3

# Add to PATH
echo 'export PATH="/home/<USER>/miniconda3/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### Step 2: Create Training Environment
```bash
# Create conda environment
conda create -n llama-training python=3.10 -y
conda activate llama-training

# Install PyTorch with CUDA support
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install training dependencies
pip install transformers datasets accelerate bitsandbytes
pip install peft trl
pip install wandb tensorboard jupyter
pip install PyYAML gitpython
pip install PyGithub requests
pip install nvidia-ml-py3 psutil
```

### Step 3: Setup Project Directory
```bash
# Create project directory
mkdir -p /data/codellama-training
cd /data/codellama-training

# Create subdirectories
mkdir -p {dataset,models,logs,scripts,configs}
```

## Phase 4: Download Project Files

### Step 1: Create Project Files on EC2
Since you'll be working directly on EC2, create these files:

```bash
cd /data/codellama-training

# Create requirements.txt
cat > requirements.txt << 'EOF'
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
accelerate>=0.24.0
peft>=0.6.0
trl>=0.7.0
bitsandbytes>=0.41.0
wandb>=0.15.0
tensorboard>=2.14.0
jupyter>=1.0.0
PyYAML>=6.0
PyGithub>=1.59.0
GitPython>=3.1.0
requests>=2.31.0
nvidia-ml-py3>=7.352.0
psutil>=5.9.0
tqdm>=4.66.0
EOF

# Install all requirements
pip install -r requirements.txt
```

### Step 2: Transfer Project Files
You have several options:

**Option A: Copy-paste files directly**
```bash
# Create each file using nano/vim
nano dataset_collector.py
# Copy-paste the content from your local files

nano train_code_llama.py
# Copy-paste the content

nano training_config.yaml
# Copy-paste the content
```

**Option B: Use GitHub (Recommended)**
```bash
# If you have the files in a GitHub repo
git clone https://github.com/YOUR_USERNAME/YOUR_REPO.git .

# Or create a new repo and push files from local machine first
```

**Option C: Use SCP from local machine**
```bash
# From your local machine
scp -i codellama-training-key.pem *.py *.yaml *.md ubuntu@YOUR_PUBLIC_IP:/data/codellama-training/
```

## Phase 5: Setup Environment Variables

### Step 1: Configure API Keys
```bash
# Edit bashrc to add environment variables
nano ~/.bashrc

# Add these lines at the end:
export GITHUB_TOKEN="your_github_personal_access_token"
export WANDB_API_KEY="your_wandb_api_key"
export HF_TOKEN="your_huggingface_token"  # Optional but recommended

# Reload bashrc
source ~/.bashrc
```

### Step 2: Test Environment
```bash
# Test Python environment
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"

# Test GitHub API
python -c "from github import Github; g = Github('$GITHUB_TOKEN'); print(f'Rate limit: {g.get_rate_limit().core.remaining}')"
```

## Phase 6: Setup Monitoring and Utilities

### Step 1: Configure Jupyter Notebook
```bash
# Generate Jupyter config
jupyter notebook --generate-config

# Configure Jupyter for remote access
cat >> ~/.jupyter/jupyter_notebook_config.py << 'EOF'
c.NotebookApp.ip = '0.0.0.0'
c.NotebookApp.open_browser = False
c.NotebookApp.port = 8888
c.NotebookApp.allow_root = False
c.NotebookApp.token = ''
c.NotebookApp.password = ''
EOF
```

### Step 2: Create Utility Scripts
```bash
# Create monitoring script
cat > /data/codellama-training/monitor.sh << 'EOF'
#!/bin/bash
echo "=== GPU Status ==="
nvidia-smi

echo -e "\n=== Disk Usage ==="
df -h

echo -e "\n=== Memory Usage ==="
free -h

echo -e "\n=== Training Processes ==="
ps aux | grep python | grep -v grep

echo -e "\n=== GPU Memory Usage ==="
nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits
EOF

chmod +x monitor.sh

# Create startup script
cat > /data/codellama-training/start_services.sh << 'EOF'
#!/bin/bash
cd /data/codellama-training
source /home/<USER>/miniconda3/bin/activate llama-training

# Start TensorBoard
tensorboard --logdir=./logs --host=0.0.0.0 --port=6006 &

# Start Jupyter
jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser &

echo "Services started!"
echo "TensorBoard: http://$(curl -s http://***************/latest/meta-data/public-ipv4):6006"
echo "Jupyter: http://$(curl -s http://***************/latest/meta-data/public-ipv4):8888"
EOF

chmod +x start_services.sh
```

## Phase 7: Data Collection and Training Workflow

### Step 1: Collect Dataset
```bash
cd /data/codellama-training
conda activate llama-training

# Run dataset collection
python dataset_collector.py

# This will create:
# - dataset/raw_data/
# - dataset/processed/
# - dataset/dataset_statistics.json
```

### Step 2: Start Training
```bash
# Start monitoring services
./start_services.sh

# Run training
python train_code_llama.py \
    --output_dir ./models/codellama-7b-finetuned \
    --num_train_epochs 3 \
    --per_device_train_batch_size 4 \
    --learning_rate 2e-4 \
    --report_to wandb

# Or use screen for long-running training
screen -S training
python train_code_llama.py --config training_config.yaml
# Ctrl+A, D to detach from screen
```

### Step 3: Monitor Training
```bash
# Check training progress
./monitor.sh

# Reattach to training screen
screen -r training

# View logs
tail -f logs/training.log
```

## Phase 8: Cost Management

### Step 1: Monitor Costs
```bash
# Install AWS CLI if needed
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS CLI
aws configure
```

### Step 2: Auto-shutdown Script
```bash
# Create auto-shutdown script to prevent runaway costs
cat > /data/codellama-training/auto_shutdown.sh << 'EOF'
#!/bin/bash
# Auto-shutdown after training completion or on idle

# Check if training is running
if ! pgrep -f "train_code_llama.py" > /dev/null; then
    echo "No training process found. Shutting down in 10 minutes..."
    sudo shutdown -h +10
fi
EOF

chmod +x auto_shutdown.sh

# Add to crontab to check every hour
(crontab -l 2>/dev/null; echo "0 * * * * /data/codellama-training/auto_shutdown.sh") | crontab -
```

## Phase 9: Backup and Recovery

### Step 1: Setup S3 Backup (Optional)
```bash
# Create S3 bucket for backups
aws s3 mb s3://your-codellama-training-backup

# Backup script
cat > /data/codellama-training/backup.sh << 'EOF'
#!/bin/bash
aws s3 sync /data/codellama-training/models/ s3://your-codellama-training-backup/models/
aws s3 sync /data/codellama-training/dataset/ s3://your-codellama-training-backup/dataset/
aws s3 sync /data/codellama-training/logs/ s3://your-codellama-training-backup/logs/
EOF

chmod +x backup.sh
```

## Quick Reference Commands

```bash
# Connect to instance
ssh -i codellama-training-key.pem ubuntu@YOUR_PUBLIC_IP

# Activate environment
conda activate llama-training

# Monitor training
./monitor.sh

# Start services
./start_services.sh

# Backup data
./backup.sh

# Check costs (after AWS CLI setup)
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 --granularity MONTHLY --metrics BlendedCost
```

This plan allows you to set up everything directly on the EC2 instance without needing the automated setup script from your local machine.
