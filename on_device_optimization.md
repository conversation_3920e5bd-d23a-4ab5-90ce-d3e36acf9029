# On-Device Optimization for Code Llama 7B

## Overview
This guide covers optimization techniques to deploy the fine-tuned Code Llama 7B model on various devices including mobile phones, edge devices, and resource-constrained environments.

## 1. Model Optimization Techniques

### Quantization
Convert model weights from 32-bit floating point to lower precision formats.

#### Post-Training Quantization (PTQ)
```python
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch.quantization as quant

def quantize_model_dynamic(model_path, output_path):
    """Apply dynamic quantization to reduce model size."""
    model = AutoModelForCausalLM.from_pretrained(model_path)
    
    # Dynamic quantization
    quantized_model = torch.quantization.quantize_dynamic(
        model, 
        {torch.nn.Linear}, 
        dtype=torch.qint8
    )
    
    # Save quantized model
    torch.save(quantized_model.state_dict(), output_path)
    return quantized_model

def quantize_model_static(model_path, calibration_data, output_path):
    """Apply static quantization with calibration data."""
    model = AutoModelForCausalLM.from_pretrained(model_path)
    
    # Prepare model for quantization
    model.eval()
    model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
    torch.quantization.prepare(model, inplace=True)
    
    # Calibrate with sample data
    with torch.no_grad():
        for batch in calibration_data:
            model(batch)
    
    # Convert to quantized model
    quantized_model = torch.quantization.convert(model, inplace=False)
    torch.save(quantized_model.state_dict(), output_path)
    
    return quantized_model
```

#### Quantization-Aware Training (QAT)
```python
import torch.quantization as quant
from torch.quantization import QuantStub, DeQuantStub

class QuantizedCodeLlama(torch.nn.Module):
    def __init__(self, model):
        super().__init__()
        self.quant = QuantStub()
        self.model = model
        self.dequant = DeQuantStub()
    
    def forward(self, x):
        x = self.quant(x)
        x = self.model(x)
        x = self.dequant(x)
        return x

def train_with_quantization(model, train_loader, num_epochs=3):
    """Train model with quantization awareness."""
    # Prepare model for QAT
    model.train()
    model.qconfig = torch.quantization.get_default_qat_qconfig('fbgemm')
    torch.quantization.prepare_qat(model, inplace=True)
    
    # Training loop with quantization
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-5)
    
    for epoch in range(num_epochs):
        for batch in train_loader:
            optimizer.zero_grad()
            outputs = model(**batch)
            loss = outputs.loss
            loss.backward()
            optimizer.step()
    
    # Convert to quantized model
    model.eval()
    quantized_model = torch.quantization.convert(model, inplace=False)
    return quantized_model
```

### Model Pruning
Remove unnecessary weights and connections to reduce model size.

#### Structured Pruning
```python
import torch.nn.utils.prune as prune

def prune_model_structured(model, pruning_ratio=0.2):
    """Apply structured pruning to remove entire neurons/channels."""
    for name, module in model.named_modules():
        if isinstance(module, torch.nn.Linear):
            # Prune 20% of neurons
            prune.ln_structured(
                module, 
                name='weight', 
                amount=pruning_ratio, 
                n=2, 
                dim=0
            )
    
    return model

def prune_model_unstructured(model, pruning_ratio=0.3):
    """Apply unstructured pruning to remove individual weights."""
    parameters_to_prune = []
    
    for name, module in model.named_modules():
        if isinstance(module, torch.nn.Linear):
            parameters_to_prune.append((module, 'weight'))
    
    # Global unstructured pruning
    prune.global_unstructured(
        parameters_to_prune,
        pruning_method=prune.L1Unstructured,
        amount=pruning_ratio,
    )
    
    return model
```

### Knowledge Distillation
Train a smaller student model to mimic the larger teacher model.

```python
import torch.nn.functional as F

class DistillationTrainer:
    def __init__(self, teacher_model, student_model, temperature=4.0, alpha=0.7):
        self.teacher = teacher_model
        self.student = student_model
        self.temperature = temperature
        self.alpha = alpha
        
        # Freeze teacher model
        for param in self.teacher.parameters():
            param.requires_grad = False
    
    def distillation_loss(self, student_logits, teacher_logits, labels):
        """Compute distillation loss combining soft and hard targets."""
        # Soft target loss (knowledge distillation)
        soft_loss = F.kl_div(
            F.log_softmax(student_logits / self.temperature, dim=-1),
            F.softmax(teacher_logits / self.temperature, dim=-1),
            reduction='batchmean'
        ) * (self.temperature ** 2)
        
        # Hard target loss (standard cross-entropy)
        hard_loss = F.cross_entropy(student_logits, labels)
        
        # Combined loss
        total_loss = self.alpha * soft_loss + (1 - self.alpha) * hard_loss
        return total_loss
    
    def train_step(self, batch, optimizer):
        """Single training step with distillation."""
        self.student.train()
        self.teacher.eval()
        
        with torch.no_grad():
            teacher_outputs = self.teacher(**batch)
            teacher_logits = teacher_outputs.logits
        
        student_outputs = self.student(**batch)
        student_logits = student_outputs.logits
        
        loss = self.distillation_loss(
            student_logits, teacher_logits, batch['labels']
        )
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        return loss.item()
```

## 2. Model Compression Formats

### ONNX Export
```python
import torch
import onnx
from transformers import AutoModelForCausalLM, AutoTokenizer

def export_to_onnx(model_path, output_path, max_length=512):
    """Export model to ONNX format for cross-platform deployment."""
    model = AutoModelForCausalLM.from_pretrained(model_path)
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # Create dummy input
    dummy_input = torch.randint(0, tokenizer.vocab_size, (1, max_length))
    
    # Export to ONNX
    torch.onnx.export(
        model,
        dummy_input,
        output_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input_ids'],
        output_names=['logits'],
        dynamic_axes={
            'input_ids': {0: 'batch_size', 1: 'sequence'},
            'logits': {0: 'batch_size', 1: 'sequence'}
        }
    )
    
    # Verify ONNX model
    onnx_model = onnx.load(output_path)
    onnx.checker.check_model(onnx_model)
    
    print(f"Model exported to {output_path}")

def optimize_onnx_model(input_path, output_path):
    """Optimize ONNX model for inference."""
    import onnxruntime as ort
    from onnxruntime.tools import optimizer
    
    # Optimize model
    optimized_model = optimizer.optimize_model(
        input_path,
        model_type='bert',  # Use bert optimizer for transformer models
        num_heads=32,       # Adjust based on your model
        hidden_size=4096    # Adjust based on your model
    )
    
    optimized_model.save_model_to_file(output_path)
    print(f"Optimized model saved to {output_path}")
```

### TensorRT Optimization
```python
import tensorrt as trt
import pycuda.driver as cuda
import pycuda.autoinit

def convert_onnx_to_tensorrt(onnx_path, engine_path, max_batch_size=1):
    """Convert ONNX model to TensorRT engine for GPU inference."""
    logger = trt.Logger(trt.Logger.WARNING)
    builder = trt.Builder(logger)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    parser = trt.OnnxParser(network, logger)
    
    # Parse ONNX model
    with open(onnx_path, 'rb') as model:
        if not parser.parse(model.read()):
            for error in range(parser.num_errors):
                print(parser.get_error(error))
            return None
    
    # Configure builder
    config = builder.create_builder_config()
    config.max_workspace_size = 1 << 30  # 1GB
    config.set_flag(trt.BuilderFlag.FP16)  # Enable FP16 precision
    
    # Build engine
    engine = builder.build_engine(network, config)
    
    # Save engine
    with open(engine_path, 'wb') as f:
        f.write(engine.serialize())
    
    print(f"TensorRT engine saved to {engine_path}")
    return engine
```

## 3. Mobile Deployment

### iOS Deployment (Core ML)
```python
import coremltools as ct
import torch

def convert_to_coreml(model_path, output_path, max_length=512):
    """Convert PyTorch model to Core ML format for iOS."""
    model = torch.jit.load(model_path)  # Load TorchScript model
    model.eval()
    
    # Create example input
    example_input = torch.randint(0, 50000, (1, max_length))
    
    # Trace model
    traced_model = torch.jit.trace(model, example_input)
    
    # Convert to Core ML
    coreml_model = ct.convert(
        traced_model,
        inputs=[ct.TensorType(shape=(1, max_length), dtype=ct.int32)],
        outputs=[ct.TensorType(dtype=ct.float32)],
        minimum_deployment_target=ct.target.iOS14
    )
    
    # Save Core ML model
    coreml_model.save(output_path)
    print(f"Core ML model saved to {output_path}")

# Swift code for iOS integration
ios_integration_code = '''
import CoreML
import Foundation

class CodeLlamaModel {
    private var model: MLModel?
    
    init() {
        guard let modelURL = Bundle.main.url(forResource: "CodeLlama", withExtension: "mlmodelc"),
              let model = try? MLModel(contentsOf: modelURL) else {
            fatalError("Failed to load Core ML model")
        }
        self.model = model
    }
    
    func predict(inputIds: [Int32]) -> [Float]? {
        guard let model = model else { return nil }
        
        do {
            let input = try MLDictionaryFeatureProvider(dictionary: [
                "input_ids": MLMultiArray(inputIds)
            ])
            
            let output = try model.prediction(from: input)
            
            if let logits = output.featureValue(for: "logits")?.multiArrayValue {
                return Array(UnsafeBufferPointer(start: logits.dataPointer.bindMemory(to: Float.self, capacity: logits.count),
                                               count: logits.count))
            }
        } catch {
            print("Prediction error: \\(error)")
        }
        
        return nil
    }
}
'''
```

### Android Deployment (TensorFlow Lite)
```python
import tensorflow as tf

def convert_to_tflite(saved_model_path, output_path, quantize=True):
    """Convert TensorFlow SavedModel to TensorFlow Lite."""
    converter = tf.lite.TFLiteConverter.from_saved_model(saved_model_path)
    
    if quantize:
        # Enable quantization
        converter.optimizations = [tf.lite.Optimize.DEFAULT]
        converter.target_spec.supported_types = [tf.float16]
    
    # Convert model
    tflite_model = converter.convert()
    
    # Save TFLite model
    with open(output_path, 'wb') as f:
        f.write(tflite_model)
    
    print(f"TensorFlow Lite model saved to {output_path}")

# Kotlin code for Android integration
android_integration_code = '''
import org.tensorflow.lite.Interpreter
import java.nio.ByteBuffer
import java.nio.ByteOrder

class CodeLlamaModel(private val modelPath: String) {
    private var interpreter: Interpreter? = null
    
    init {
        val options = Interpreter.Options()
        options.setNumThreads(4)
        options.setUseNNAPI(true)  // Use Android Neural Networks API
        
        interpreter = Interpreter(loadModelFile(), options)
    }
    
    private fun loadModelFile(): ByteBuffer {
        val assetManager = context.assets
        val fileDescriptor = assetManager.openFd(modelPath)
        val inputStream = FileInputStream(fileDescriptor.fileDescriptor)
        val fileChannel = inputStream.channel
        val startOffset = fileDescriptor.startOffset
        val declaredLength = fileDescriptor.declaredLength
        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength)
    }
    
    fun predict(inputIds: IntArray): FloatArray {
        val inputBuffer = ByteBuffer.allocateDirect(inputIds.size * 4)
        inputBuffer.order(ByteOrder.nativeOrder())
        
        for (id in inputIds) {
            inputBuffer.putInt(id)
        }
        
        val outputBuffer = ByteBuffer.allocateDirect(50000 * 4)  // Vocab size * 4 bytes
        outputBuffer.order(ByteOrder.nativeOrder())
        
        interpreter?.run(inputBuffer, outputBuffer)
        
        outputBuffer.rewind()
        val output = FloatArray(50000)
        outputBuffer.asFloatBuffer().get(output)
        
        return output
    }
}
'''
```

## 4. Performance Optimization

### Memory Optimization
```python
import torch
import gc

class MemoryOptimizedInference:
    def __init__(self, model_path, max_memory_mb=512):
        self.max_memory_mb = max_memory_mb
        self.model = self.load_optimized_model(model_path)
    
    def load_optimized_model(self, model_path):
        """Load model with memory optimizations."""
        # Enable memory efficient attention
        torch.backends.cuda.enable_flash_sdp(True)
        
        # Load with reduced precision
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            low_cpu_mem_usage=True
        )
        
        return model
    
    def generate_with_memory_limit(self, input_ids, max_length=100):
        """Generate text with memory constraints."""
        # Clear cache before generation
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        # Generate with gradient checkpointing
        with torch.no_grad():
            outputs = self.model.generate(
                input_ids,
                max_length=max_length,
                do_sample=True,
                temperature=0.7,
                pad_token_id=self.model.config.eos_token_id,
                use_cache=False  # Disable KV cache to save memory
            )
        
        # Clear cache after generation
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return outputs
```

### Inference Optimization
```python
import time
from typing import List, Dict

class OptimizedInference:
    def __init__(self, model_path):
        self.model = self.load_model(model_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        
        # Warm up model
        self.warmup()
    
    def warmup(self, num_warmup=5):
        """Warm up model for consistent performance."""
        dummy_input = torch.randint(0, 1000, (1, 50))
        
        for _ in range(num_warmup):
            with torch.no_grad():
                _ = self.model(dummy_input)
    
    def batch_inference(self, texts: List[str], batch_size: int = 4) -> List[str]:
        """Perform batched inference for better throughput."""
        results = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            
            # Tokenize batch
            inputs = self.tokenizer(
                batch_texts,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=512
            )
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_length=inputs.input_ids.shape[1] + 50,
                    do_sample=True,
                    temperature=0.7,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode results
            batch_results = self.tokenizer.batch_decode(
                outputs[:, inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )
            
            results.extend(batch_results)
        
        return results
    
    def benchmark_inference(self, num_samples=100):
        """Benchmark inference performance."""
        dummy_input = torch.randint(0, 1000, (1, 100))
        
        # Warm up
        for _ in range(10):
            with torch.no_grad():
                _ = self.model(dummy_input)
        
        # Benchmark
        start_time = time.time()
        
        for _ in range(num_samples):
            with torch.no_grad():
                _ = self.model(dummy_input)
        
        end_time = time.time()
        
        avg_latency = (end_time - start_time) / num_samples * 1000  # ms
        throughput = num_samples / (end_time - start_time)  # samples/sec
        
        print(f"Average latency: {avg_latency:.2f} ms")
        print(f"Throughput: {throughput:.2f} samples/sec")
        
        return {"latency_ms": avg_latency, "throughput": throughput}
```

## 5. Deployment Pipeline

### Complete Optimization Pipeline
```python
class ModelOptimizationPipeline:
    def __init__(self, model_path, target_platform="mobile"):
        self.model_path = model_path
        self.target_platform = target_platform
        self.optimization_steps = []
    
    def add_quantization(self, method="dynamic", bits=8):
        """Add quantization step."""
        self.optimization_steps.append(("quantization", method, bits))
        return self
    
    def add_pruning(self, ratio=0.2, method="structured"):
        """Add pruning step."""
        self.optimization_steps.append(("pruning", method, ratio))
        return self
    
    def add_distillation(self, student_config):
        """Add knowledge distillation step."""
        self.optimization_steps.append(("distillation", student_config))
        return self
    
    def execute_pipeline(self, output_dir):
        """Execute the complete optimization pipeline."""
        current_model_path = self.model_path
        
        for step in self.optimization_steps:
            if step[0] == "quantization":
                current_model_path = self.apply_quantization(
                    current_model_path, step[1], step[2], output_dir
                )
            elif step[0] == "pruning":
                current_model_path = self.apply_pruning(
                    current_model_path, step[1], step[2], output_dir
                )
            elif step[0] == "distillation":
                current_model_path = self.apply_distillation(
                    current_model_path, step[1], output_dir
                )
        
        # Final export based on target platform
        if self.target_platform == "ios":
            self.export_coreml(current_model_path, output_dir)
        elif self.target_platform == "android":
            self.export_tflite(current_model_path, output_dir)
        elif self.target_platform == "onnx":
            self.export_onnx(current_model_path, output_dir)
        
        return current_model_path

# Example usage
pipeline = ModelOptimizationPipeline("./models/codellama-7b-finetuned", "mobile")
pipeline.add_quantization("dynamic", 8).add_pruning(0.2, "structured")
optimized_model = pipeline.execute_pipeline("./optimized_models/")
```

## 6. Performance Benchmarks

### Expected Performance Metrics
- **Original Model**: 7B parameters, ~13GB memory
- **Quantized (INT8)**: ~3.5GB memory, 2-3x faster inference
- **Pruned (20%)**: ~5.6B parameters, 1.5x faster inference
- **Quantized + Pruned**: ~2.8GB memory, 3-4x faster inference
- **Distilled (1B)**: ~2GB memory, 5-7x faster inference

### Target Device Performance
- **High-end Mobile**: 50-100ms per token
- **Mid-range Mobile**: 100-200ms per token
- **Edge Devices**: 200-500ms per token
- **CPU-only**: 500-1000ms per token

This optimization guide provides a comprehensive approach to deploying Code Llama 7B on various devices while maintaining reasonable performance and accuracy.
