# Multi-Language Code Dataset Creation for Code Llama 7B

## Overview
This guide outlines the creation of a comprehensive training dataset covering web development, systems programming, mobile development, and game engine development.

## 1. Dataset Structure

### Target Languages & Frameworks

#### Web Development
- **Backend**: Python (Django, FastAPI, Flask), PHP (Laravel, Symfony), Java (Spring Boot)
- **Frontend**: JavaScript/TypeScript (React, Vue, Angular), HTML/CSS
- **Full-stack**: Node.js, Next.js, Nuxt.js

#### Systems Programming
- **Low-level**: C, C++, Rust, Go
- **Scripting**: Bash, PowerShell
- **Assembly**: x86, ARM

#### Mobile Development
- **Native**: <PERSON><PERSON><PERSON> (Android), Swift (iOS)
- **Cross-platform**: <PERSON>lutter (Dart), React Native (JavaScript)
- **Frameworks**: Django REST, Falcon API

#### Game Development
- **Unity**: C#
- **Godot**: GDScript, C#
- **Unreal**: C++, Blueprint

## 2. Data Sources

### Primary Sources
1. **GitHub Repositories** (Public, high-quality projects)
2. **Stack Overflow** (Q&A pairs)
3. **Documentation** (Official docs, tutorials)
4. **Code Competition Platforms** (LeetCode, HackerRank)
5. **Open Source Projects** (Apache, Mozilla, etc.)

### Quality Criteria
- **Stars**: >100 for repositories
- **Recent Activity**: Updated within 2 years
- **Documentation**: Well-documented code
- **License**: Permissive licenses (MIT, Apache, BSD)

## 3. Dataset Collection Pipeline

### GitHub Data Collection
```python
import requests
import json
from github import Github
import os

class GitHubDataCollector:
    def __init__(self, token):
        self.github = Github(token)
        self.languages = {
            'web': ['python', 'javascript', 'typescript', 'php', 'java'],
            'systems': ['c', 'cpp', 'rust', 'go'],
            'mobile': ['kotlin', 'swift', 'dart'],
            'game': ['csharp', 'gdscript']
        }
    
    def collect_repositories(self, language, min_stars=100, max_repos=1000):
        """Collect high-quality repositories for a specific language"""
        query = f"language:{language} stars:>{min_stars} sort:stars"
        repos = self.github.search_repositories(query=query)
        
        collected = []
        for repo in repos[:max_repos]:
            if self.is_quality_repo(repo):
                collected.append({
                    'name': repo.full_name,
                    'stars': repo.stargazers_count,
                    'language': repo.language,
                    'description': repo.description,
                    'clone_url': repo.clone_url
                })
        return collected
    
    def is_quality_repo(self, repo):
        """Filter for high-quality repositories"""
        return (
            repo.stargazers_count > 100 and
            repo.updated_at.year >= 2022 and
            repo.size < 100000 and  # Not too large
            not repo.fork and
            repo.license and
            repo.license.key in ['mit', 'apache-2.0', 'bsd-3-clause']
        )
```

### Code Extraction & Processing
```python
import ast
import re
from pathlib import Path

class CodeProcessor:
    def __init__(self):
        self.file_extensions = {
            'python': ['.py'],
            'javascript': ['.js', '.jsx', '.ts', '.tsx'],
            'java': ['.java'],
            'cpp': ['.cpp', '.cc', '.cxx', '.h', '.hpp'],
            'c': ['.c', '.h'],
            'rust': ['.rs'],
            'go': ['.go'],
            'kotlin': ['.kt', '.kts'],
            'swift': ['.swift'],
            'dart': ['.dart'],
            'csharp': ['.cs'],
            'php': ['.php']
        }
    
    def extract_functions(self, file_path, language):
        """Extract functions/methods from code files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if language == 'python':
                return self.extract_python_functions(content)
            elif language in ['javascript', 'typescript']:
                return self.extract_js_functions(content)
            # Add more language-specific extractors
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return []
    
    def extract_python_functions(self, content):
        """Extract Python functions with docstrings"""
        try:
            tree = ast.parse(content)
            functions = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_code = ast.get_source_segment(content, node)
                    docstring = ast.get_docstring(node)
                    
                    functions.append({
                        'name': node.name,
                        'code': func_code,
                        'docstring': docstring,
                        'line_start': node.lineno,
                        'line_end': node.end_lineno
                    })
            return functions
        except:
            return []
```

## 4. Dataset Format

### Training Sample Structure
```json
{
    "instruction": "Write a Python function to calculate fibonacci numbers",
    "input": "def fibonacci(n):",
    "output": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)",
    "language": "python",
    "category": "algorithms",
    "difficulty": "easy",
    "source": "github",
    "metadata": {
        "repo": "user/repo",
        "file": "math_utils.py",
        "stars": 1500,
        "license": "MIT"
    }
}
```

### Instruction Templates
```python
INSTRUCTION_TEMPLATES = {
    'function_completion': [
        "Complete this {language} function:",
        "Implement the following {language} function:",
        "Write a {language} function that {description}:"
    ],
    'bug_fixing': [
        "Fix the bug in this {language} code:",
        "Debug and correct this {language} function:",
        "Identify and fix the error in this {language} code:"
    ],
    'code_explanation': [
        "Explain what this {language} code does:",
        "Describe the functionality of this {language} function:",
        "Comment this {language} code:"
    ],
    'optimization': [
        "Optimize this {language} code for better performance:",
        "Improve the efficiency of this {language} function:",
        "Refactor this {language} code:"
    ]
}
```

## 5. Data Collection Scripts

### Main Collection Script
```python
#!/usr/bin/env python3
import os
import json
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from github_collector import GitHubDataCollector
from code_processor import CodeProcessor

class DatasetBuilder:
    def __init__(self, output_dir="./dataset"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.github_token = os.getenv('GITHUB_TOKEN')
        self.collector = GitHubDataCollector(self.github_token)
        self.processor = CodeProcessor()
    
    def build_dataset(self):
        """Main dataset building pipeline"""
        print("Starting dataset collection...")
        
        # Collect repositories for each language category
        all_repos = {}
        for category, languages in self.collector.languages.items():
            print(f"Collecting {category} repositories...")
            category_repos = []
            
            for lang in languages:
                repos = self.collector.collect_repositories(lang, min_stars=100)
                category_repos.extend(repos)
                time.sleep(1)  # Rate limiting
            
            all_repos[category] = category_repos
            self.save_repos_list(category, category_repos)
        
        # Process repositories and extract code samples
        self.process_repositories(all_repos)
        
        # Generate training samples
        self.generate_training_samples()
    
    def save_repos_list(self, category, repos):
        """Save repository list for a category"""
        with open(self.output_dir / f"{category}_repos.json", 'w') as f:
            json.dump(repos, f, indent=2)
    
    def process_repositories(self, all_repos):
        """Clone and process repositories"""
        for category, repos in all_repos.items():
            print(f"Processing {category} repositories...")
            
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                for repo in repos[:50]:  # Limit for initial dataset
                    future = executor.submit(self.process_single_repo, repo, category)
                    futures.append(future)
                
                for future in futures:
                    try:
                        future.result(timeout=300)  # 5 minute timeout
                    except Exception as e:
                        print(f"Error processing repository: {e}")

if __name__ == "__main__":
    builder = DatasetBuilder()
    builder.build_dataset()
```

## 6. Quality Assurance

### Data Validation
```python
def validate_sample(sample):
    """Validate training sample quality"""
    checks = [
        len(sample['output']) > 10,  # Minimum length
        len(sample['output']) < 2000,  # Maximum length
        sample['language'] in SUPPORTED_LANGUAGES,
        not contains_sensitive_data(sample['output']),
        is_valid_syntax(sample['output'], sample['language'])
    ]
    return all(checks)

def remove_duplicates(samples):
    """Remove duplicate code samples"""
    seen = set()
    unique_samples = []
    
    for sample in samples:
        code_hash = hash(sample['output'])
        if code_hash not in seen:
            seen.add(code_hash)
            unique_samples.append(sample)
    
    return unique_samples
```

### Dataset Statistics
```python
def generate_dataset_stats(samples):
    """Generate comprehensive dataset statistics"""
    stats = {
        'total_samples': len(samples),
        'languages': {},
        'categories': {},
        'avg_length': sum(len(s['output']) for s in samples) / len(samples)
    }
    
    for sample in samples:
        lang = sample['language']
        cat = sample['category']
        
        stats['languages'][lang] = stats['languages'].get(lang, 0) + 1
        stats['categories'][cat] = stats['categories'].get(cat, 0) + 1
    
    return stats
```

## 7. Dataset Splits

### Train/Validation/Test Split
```python
def create_splits(samples, train_ratio=0.8, val_ratio=0.1):
    """Create balanced train/validation/test splits"""
    import random
    random.shuffle(samples)
    
    total = len(samples)
    train_size = int(total * train_ratio)
    val_size = int(total * val_ratio)
    
    return {
        'train': samples[:train_size],
        'validation': samples[train_size:train_size + val_size],
        'test': samples[train_size + val_size:]
    }
```

## 8. Expected Dataset Size

### Target Distribution
- **Total Samples**: 100,000 - 200,000
- **Web Development**: 40% (40K-80K samples)
- **Systems Programming**: 25% (25K-50K samples)
- **Mobile Development**: 20% (20K-40K samples)
- **Game Development**: 15% (15K-30K samples)

### Storage Requirements
- **Raw Data**: 5-10 GB
- **Processed Dataset**: 2-5 GB
- **Compressed**: 1-2 GB

## Next Steps
1. Set up GitHub API access
2. Run data collection pipeline
3. Validate and clean collected data
4. Generate instruction-following samples
5. Create balanced train/val/test splits
