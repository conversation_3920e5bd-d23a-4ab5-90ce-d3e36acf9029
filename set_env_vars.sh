#!/bin/bash
# Environment Variables Setup
# Edit this file with your actual API keys before running

# GitHub Personal Access Token (REQUIRED for dataset collection)
# Get from: https://github.com/settings/tokens
# Needs 'public_repo' scope
export GITHUB_TOKEN="your_github_token_here"

# Weights & Biases API Key (OPTIONAL - for training monitoring)
# Get from: https://wandb.ai/settings
# Sign up at wandb.ai if you don't have an account
export WANDB_API_KEY="your_wandb_key_here"

# Hugging Face Token (OPTIONAL - for model downloads)
# Get from: https://huggingface.co/settings/tokens
# Useful for accessing gated models
export HF_TOKEN="your_hf_token_here"

# AWS credentials (OPTIONAL - for S3 backup)
# export AWS_ACCESS_KEY_ID="your_aws_access_key"
# export AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
# export AWS_DEFAULT_REGION="us-east-1"

# Display status
echo "=== Environment Variables Set ==="
if [ "$GITHUB_TOKEN" != "your_github_token_here" ]; then
    echo "✓ GitHub Token: Set"
else
    echo "✗ GitHub Token: NOT SET (required for dataset collection)"
fi

if [ "$WANDB_API_KEY" != "your_wandb_key_here" ]; then
    echo "✓ Weights & Biases: Set"
else
    echo "- Weights & Biases: Not set (optional)"
fi

if [ "$HF_TOKEN" != "your_hf_token_here" ]; then
    echo "✓ Hugging Face: Set"
else
    echo "- Hugging Face: Not set (optional)"
fi

echo ""
echo "To edit this file: nano set_env_vars.sh"
echo "To apply changes: source set_env_vars.sh"
