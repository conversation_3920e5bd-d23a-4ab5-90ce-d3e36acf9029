# Code Llama 7B Fine-tuning on AWS: Cost-Optimized Approach

## Overview
This document outlines a cost-effective approach to fine-tune Code Llama 7B on AWS for multi-language code generation, targeting web development, systems programming, mobile development, and game engines.

## 1. AWS Instance Selection & Cost Optimization

### Recommended Instance Types (Cost-Performance Optimized)

#### Primary Option: g4dn.xlarge
- **GPU**: 1x NVIDIA T4 (16GB VRAM)
- **vCPUs**: 4
- **RAM**: 16 GB
- **Storage**: 125 GB NVMe SSD
- **Cost**: ~$0.526/hour (On-Demand)
- **Spot Price**: ~$0.158/hour (70% savings)

#### Alternative: g4dn.2xlarge (for faster training)
- **GPU**: 1x NVIDIA T4 (16GB VRAM)
- **vCPUs**: 8
- **RAM**: 32 GB
- **Storage**: 225 GB NVMe SSD
- **Cost**: ~$0.752/hour (On-Demand)
- **Spot Price**: ~$0.226/hour

#### Budget Option: p3.2xlarge (if available as spot)
- **GPU**: 1x NVIDIA V100 (16GB VRAM)
- **vCPUs**: 8
- **RAM**: 61 GB
- **Cost**: ~$3.06/hour (On-Demand)
- **Spot Price**: ~$0.918/hour (when available)

### Cost Optimization Strategies

1. **Use Spot Instances**: 60-90% cost reduction
2. **Reserved Instances**: For long-term training (1-3 years)
3. **Scheduled Instances**: For predictable training windows
4. **Auto-scaling**: Scale down during idle periods
5. **EBS GP3 Storage**: More cost-effective than GP2

## 2. AMI Selection

### Recommended AMI: Deep Learning AMI (Ubuntu 20.04)
- **AMI ID**: ami-0c02fb55956c7d316 (us-east-1)
- **Pre-installed**: CUDA 11.8, cuDNN, PyTorch, Transformers
- **Benefits**: Reduces setup time, optimized drivers

### Alternative: Ubuntu 20.04 LTS (Manual Setup)
- **AMI ID**: ami-0a634ae95e11c6f91 (us-east-1)
- **Benefits**: Lower cost, full control over environment

## 3. Infrastructure Setup

### Storage Configuration
```bash
# EBS Volume Setup
- Root Volume: 50 GB GP3 (for OS and software)
- Data Volume: 500 GB GP3 (for datasets and models)
- Backup Volume: 100 GB GP3 (for checkpoints)
```

### Network Configuration
- VPC with public subnet for SSH access
- Security Group: SSH (22), Jupyter (8888), TensorBoard (6006)
- Elastic IP for consistent access

## 4. Environment Setup Script

### Initial Setup
```bash
#!/bin/bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y git wget curl htop nvtop screen tmux

# Install Miniconda
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -b
echo 'export PATH="$HOME/miniconda3/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Create training environment
conda create -n llama-training python=3.10 -y
conda activate llama-training

# Install PyTorch with CUDA support
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install training dependencies
pip install transformers datasets accelerate bitsandbytes
pip install wandb tensorboard jupyter
pip install peft trl
```

## 5. Training Configuration

### Model Configuration
```python
# Model: Code Llama 7B
model_name = "codellama/CodeLlama-7b-hf"

# Training Parameters
training_config = {
    "learning_rate": 2e-4,
    "batch_size": 4,  # Adjust based on GPU memory
    "gradient_accumulation_steps": 4,
    "max_length": 2048,
    "num_epochs": 3,
    "warmup_steps": 100,
    "save_steps": 500,
    "eval_steps": 500,
    "logging_steps": 10,
}

# LoRA Configuration (for memory efficiency)
lora_config = {
    "r": 16,
    "lora_alpha": 32,
    "target_modules": ["q_proj", "v_proj"],
    "lora_dropout": 0.1,
    "bias": "none",
    "task_type": "CAUSAL_LM",
}
```

### Memory Optimization
```python
# 4-bit quantization for memory efficiency
quantization_config = {
    "load_in_4bit": True,
    "bnb_4bit_compute_dtype": torch.float16,
    "bnb_4bit_use_double_quant": True,
    "bnb_4bit_quant_type": "nf4",
}
```

## 6. Cost Estimation

### Training Duration Estimates
- **Dataset Size**: 100K samples
- **Training Time**: 24-48 hours (g4dn.xlarge)
- **Spot Instance Cost**: $3.79 - $7.58
- **On-Demand Cost**: $12.62 - $25.25

### Monthly Development Cost (Spot Instances)
- **Training**: $50-100/month
- **Storage**: $25-50/month
- **Data Transfer**: $5-10/month
- **Total**: $80-160/month

## 7. Monitoring & Management

### Cost Monitoring
```bash
# AWS CLI cost monitoring
aws ce get-cost-and-usage \
    --time-period Start=2024-01-01,End=2024-01-31 \
    --granularity MONTHLY \
    --metrics BlendedCost
```

### Training Monitoring
- **Weights & Biases**: For experiment tracking
- **TensorBoard**: For loss visualization
- **CloudWatch**: For instance monitoring
- **Custom Scripts**: For GPU utilization

## 8. Backup & Recovery

### Checkpoint Strategy
```python
# Save checkpoints every 500 steps
checkpoint_config = {
    "save_steps": 500,
    "save_total_limit": 3,
    "output_dir": "/data/checkpoints",
}

# Automatic S3 backup
aws s3 sync /data/checkpoints s3://your-bucket/checkpoints/
```

## 9. Security Best Practices

1. **IAM Roles**: Minimal permissions for EC2 instances
2. **VPC**: Private subnets for training instances
3. **Encryption**: EBS volumes and S3 buckets
4. **Access Control**: SSH key pairs, no password auth
5. **Monitoring**: CloudTrail for API calls

## 10. Scaling Strategies

### Multi-GPU Training (Future)
- **p3.8xlarge**: 4x V100 GPUs
- **g4dn.12xlarge**: 4x T4 GPUs
- **Data Parallel**: Distribute batches across GPUs

### Distributed Training
- **Multiple Instances**: Use AWS ParallelCluster
- **Model Parallel**: For larger models
- **Pipeline Parallel**: For memory constraints

## Next Steps
1. Launch spot instance with Deep Learning AMI
2. Set up training environment
3. Prepare multi-language dataset
4. Configure LoRA fine-tuning
5. Monitor training progress and costs
