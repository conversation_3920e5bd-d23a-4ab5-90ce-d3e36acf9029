#!/usr/bin/env python3
"""
Multi-Language Code Dataset Collector for Code Llama 7B Fine-tuning
Collects high-quality code samples from GitHub repositories across multiple programming languages.
"""

import os
import json
import time
import random
import hashlib
from pathlib import Path
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from github import Github
import git
import tempfile
import shutil

class GitHubDataCollector:
    """Collects code repositories from GitHub based on quality criteria."""
    
    def __init__(self, token: str):
        self.github = Github(token)
        self.session = requests.Session()
        self.session.headers.update({'Authorization': f'token {token}'})
        
        # Language mappings for GitHub API
        self.languages = {
            'web_backend': ['Python', 'PHP', 'Java', 'JavaScript', 'TypeScript'],
            'web_frontend': ['JavaScript', 'TypeScript', 'HTML', 'CSS'],
            'systems': ['C', 'C++', 'Rust', 'Go', 'Assembly'],
            'mobile': ['Kotlin', 'Swift', 'Dart', 'Java'],
            'game': ['C#', 'C++', 'GDScript', 'UnrealScript']
        }
        
        # File extensions for each language
        self.extensions = {
            'Python': ['.py'],
            'JavaScript': ['.js', '.jsx'],
            'TypeScript': ['.ts', '.tsx'],
            'Java': ['.java'],
            'PHP': ['.php'],
            'C': ['.c', '.h'],
            'C++': ['.cpp', '.cc', '.cxx', '.hpp', '.h'],
            'Rust': ['.rs'],
            'Go': ['.go'],
            'Kotlin': ['.kt', '.kts'],
            'Swift': ['.swift'],
            'Dart': ['.dart'],
            'C#': ['.cs'],
            'HTML': ['.html', '.htm'],
            'CSS': ['.css', '.scss', '.sass'],
            'GDScript': ['.gd']
        }
    
    def search_repositories(self, language: str, min_stars: int = 100, max_repos: int = 100) -> List[Dict]:
        """Search for high-quality repositories in a specific language."""
        print(f"Searching for {language} repositories...")
        
        query = f"language:{language} stars:>{min_stars} sort:stars-desc"
        repositories = []
        
        try:
            repos = self.github.search_repositories(query=query)
            
            for i, repo in enumerate(repos):
                if i >= max_repos:
                    break
                    
                if self._is_quality_repo(repo):
                    repo_data = {
                        'name': repo.full_name,
                        'stars': repo.stargazers_count,
                        'language': repo.language,
                        'description': repo.description or '',
                        'clone_url': repo.clone_url,
                        'size': repo.size,
                        'updated_at': repo.updated_at.isoformat(),
                        'license': repo.license.key if repo.license else None,
                        'topics': repo.get_topics()
                    }
                    repositories.append(repo_data)
                
                # Rate limiting
                if i % 10 == 0:
                    time.sleep(1)
                    
        except Exception as e:
            print(f"Error searching repositories for {language}: {e}")
        
        print(f"Found {len(repositories)} quality {language} repositories")
        return repositories
    
    def _is_quality_repo(self, repo) -> bool:
        """Check if repository meets quality criteria."""
        try:
            return (
                repo.stargazers_count >= 100 and
                repo.updated_at.year >= 2022 and
                repo.size < 50000 and  # Not too large (KB)
                not repo.fork and
                repo.license and
                repo.license.key in ['mit', 'apache-2.0', 'bsd-3-clause', 'gpl-3.0', 'lgpl-3.0'] and
                repo.description and
                len(repo.description) > 10
            )
        except:
            return False

class CodeProcessor:
    """Processes code files and extracts training samples."""
    
    def __init__(self):
        self.instruction_templates = {
            'function_completion': [
                "Complete this {language} function:",
                "Implement the following {language} function:",
                "Write a {language} function that {description}:",
                "Fill in the missing code for this {language} function:"
            ],
            'code_explanation': [
                "Explain what this {language} code does:",
                "Describe the functionality of this {language} function:",
                "Add comments to explain this {language} code:",
                "What is the purpose of this {language} code?"
            ],
            'bug_fixing': [
                "Fix the bug in this {language} code:",
                "Debug and correct this {language} function:",
                "Identify and fix the error in this {language} code:",
                "Repair the following {language} code:"
            ],
            'optimization': [
                "Optimize this {language} code for better performance:",
                "Improve the efficiency of this {language} function:",
                "Refactor this {language} code for better readability:",
                "Enhance this {language} code:"
            ]
        }
    
    def extract_code_samples(self, repo_path: Path, language: str, max_samples: int = 50) -> List[Dict]:
        """Extract code samples from a repository."""
        samples = []
        extensions = GitHubDataCollector().extensions.get(language, [])
        
        try:
            # Find all relevant files
            code_files = []
            for ext in extensions:
                code_files.extend(repo_path.rglob(f"*{ext}"))
            
            # Limit number of files to process
            if len(code_files) > 100:
                code_files = random.sample(code_files, 100)
            
            for file_path in code_files:
                if file_path.stat().st_size > 50000:  # Skip very large files
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # Extract functions/classes based on language
                    if language == 'Python':
                        file_samples = self._extract_python_samples(content, file_path)
                    elif language in ['JavaScript', 'TypeScript']:
                        file_samples = self._extract_js_samples(content, file_path)
                    elif language == 'Java':
                        file_samples = self._extract_java_samples(content, file_path)
                    else:
                        file_samples = self._extract_generic_samples(content, file_path, language)
                    
                    samples.extend(file_samples)
                    
                    if len(samples) >= max_samples:
                        break
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"Error processing repository {repo_path}: {e}")
        
        return samples[:max_samples]
    
    def _extract_python_samples(self, content: str, file_path: Path) -> List[Dict]:
        """Extract Python functions and classes."""
        import ast
        samples = []
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    try:
                        func_lines = content.split('\n')[node.lineno-1:node.end_lineno]
                        func_code = '\n'.join(func_lines)
                        
                        if len(func_code) > 50 and len(func_code) < 1000:
                            sample = self._create_sample(
                                code=func_code,
                                language='Python',
                                file_path=file_path,
                                sample_type='function',
                                name=node.name
                            )
                            samples.append(sample)
                    except:
                        continue
                        
        except:
            pass
            
        return samples
    
    def _extract_js_samples(self, content: str, file_path: Path) -> List[Dict]:
        """Extract JavaScript/TypeScript functions."""
        import re
        samples = []
        
        # Simple regex patterns for function extraction
        patterns = [
            r'function\s+\w+\s*\([^)]*\)\s*{[^}]*}',
            r'const\s+\w+\s*=\s*\([^)]*\)\s*=>\s*{[^}]*}',
            r'export\s+function\s+\w+\s*\([^)]*\)\s*{[^}]*}'
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
            for match in matches:
                func_code = match.group(0)
                if len(func_code) > 50 and len(func_code) < 1000:
                    sample = self._create_sample(
                        code=func_code,
                        language='JavaScript',
                        file_path=file_path,
                        sample_type='function'
                    )
                    samples.append(sample)
        
        return samples
    
    def _extract_java_samples(self, content: str, file_path: Path) -> List[Dict]:
        """Extract Java methods."""
        import re
        samples = []
        
        # Simple regex for Java methods
        pattern = r'(public|private|protected)?\s*(static)?\s*\w+\s+\w+\s*\([^)]*\)\s*{[^}]*}'
        matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
        
        for match in matches:
            method_code = match.group(0)
            if len(method_code) > 50 and len(method_code) < 1000:
                sample = self._create_sample(
                    code=method_code,
                    language='Java',
                    file_path=file_path,
                    sample_type='method'
                )
                samples.append(sample)
        
        return samples
    
    def _extract_generic_samples(self, content: str, file_path: Path, language: str) -> List[Dict]:
        """Extract code samples for other languages."""
        samples = []
        
        # Split content into logical blocks
        lines = content.split('\n')
        current_block = []
        
        for line in lines:
            if line.strip():
                current_block.append(line)
            else:
                if len(current_block) > 5 and len(current_block) < 50:
                    block_code = '\n'.join(current_block)
                    if len(block_code) > 100:
                        sample = self._create_sample(
                            code=block_code,
                            language=language,
                            file_path=file_path,
                            sample_type='code_block'
                        )
                        samples.append(sample)
                current_block = []
        
        return samples
    
    def _create_sample(self, code: str, language: str, file_path: Path, 
                      sample_type: str = 'function', name: str = None) -> Dict:
        """Create a training sample from code."""
        # Generate instruction
        template_type = random.choice(list(self.instruction_templates.keys()))
        template = random.choice(self.instruction_templates[template_type])
        
        instruction = template.format(
            language=language,
            description=f"performs {sample_type} operations"
        )
        
        # Create sample
        sample = {
            'instruction': instruction,
            'input': '',
            'output': code,
            'language': language.lower(),
            'category': self._categorize_code(code, language),
            'sample_type': sample_type,
            'template_type': template_type,
            'metadata': {
                'file_path': str(file_path),
                'name': name,
                'length': len(code),
                'hash': hashlib.md5(code.encode()).hexdigest()
            }
        }
        
        return sample
    
    def _categorize_code(self, code: str, language: str) -> str:
        """Categorize code based on content and language."""
        code_lower = code.lower()
        
        if any(keyword in code_lower for keyword in ['test', 'assert', 'unittest']):
            return 'testing'
        elif any(keyword in code_lower for keyword in ['api', 'request', 'response', 'http']):
            return 'web_api'
        elif any(keyword in code_lower for keyword in ['database', 'sql', 'query']):
            return 'database'
        elif any(keyword in code_lower for keyword in ['algorithm', 'sort', 'search']):
            return 'algorithms'
        elif language.lower() in ['c', 'c++', 'rust']:
            return 'systems'
        elif language.lower() in ['kotlin', 'swift', 'dart']:
            return 'mobile'
        elif language.lower() in ['c#', 'gdscript']:
            return 'game'
        else:
            return 'general'

class DatasetBuilder:
    """Main class for building the training dataset."""
    
    def __init__(self, output_dir: str = "./dataset", github_token: str = None):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.github_token = github_token or os.getenv('GITHUB_TOKEN')
        if not self.github_token:
            raise ValueError("GitHub token is required. Set GITHUB_TOKEN environment variable.")
        
        self.collector = GitHubDataCollector(self.github_token)
        self.processor = CodeProcessor()
        
        # Create subdirectories
        (self.output_dir / 'raw_data').mkdir(exist_ok=True)
        (self.output_dir / 'processed').mkdir(exist_ok=True)
        (self.output_dir / 'repos').mkdir(exist_ok=True)
    
    def build_dataset(self, target_samples: int = 50000):
        """Build the complete dataset targeting specific sample count."""
        print(f"Starting dataset collection (target: {target_samples:,} samples)...")

        all_samples = []

        # Calculate samples per language category
        total_languages = sum(len(langs) for langs in self.collector.languages.values())
        samples_per_language = target_samples // total_languages

        print(f"Target samples per language: ~{samples_per_language}")

        # Process each language category
        for category, languages in self.collector.languages.items():
            print(f"\n=== Processing {category} ===")

            for language in languages:
                print(f"\nCollecting {language} repositories...")

                # Calculate repos and samples needed
                max_repos = min(15, max(5, samples_per_language // 20))  # 5-15 repos
                max_samples_per_repo = min(50, max(10, samples_per_language // max_repos))  # 10-50 samples per repo

                print(f"Target: {max_repos} repos, {max_samples_per_repo} samples/repo")

                # Search for repositories
                repos = self.collector.search_repositories(
                    language=language,
                    min_stars=100,
                    max_repos=max_repos
                )

                # Save repository list
                with open(self.output_dir / 'raw_data' / f'{language.lower()}_repos.json', 'w') as f:
                    json.dump(repos, f, indent=2)

                # Process repositories
                language_samples = self._process_language_repos(
                    repos, language, max_samples_per_repo
                )
                all_samples.extend(language_samples)

                print(f"Collected {len(language_samples)} samples for {language}")

                # Stop if we've reached our target
                if len(all_samples) >= target_samples:
                    print(f"Reached target of {target_samples:,} samples!")
                    break

            if len(all_samples) >= target_samples:
                break

        # Trim to exact target if we exceeded
        if len(all_samples) > target_samples:
            all_samples = all_samples[:target_samples]

        # Save all samples
        print(f"\nFinal dataset size: {len(all_samples):,} samples")
        self._save_dataset(all_samples)

        # Generate statistics
        self._generate_statistics(all_samples)

        print("Dataset collection completed!")
    
    def _process_language_repos(self, repos: List[Dict], language: str, max_samples: int) -> List[Dict]:
        """Process repositories for a specific language."""
        all_samples = []
        
        for repo in repos[:10]:  # Limit repos for initial dataset
            try:
                samples = self._process_single_repo(repo, language, max_samples)
                all_samples.extend(samples)
            except Exception as e:
                print(f"Error processing {repo['name']}: {e}")
                continue
        
        return all_samples
    
    def _process_single_repo(self, repo: Dict, language: str, max_samples: int) -> List[Dict]:
        """Process a single repository."""
        print(f"Processing {repo['name']}...")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            repo_path = Path(temp_dir) / 'repo'
            
            try:
                # Clone repository
                git.Repo.clone_from(repo['clone_url'], repo_path, depth=1)
                
                # Extract samples
                samples = self.processor.extract_code_samples(repo_path, language, max_samples)
                
                # Add repository metadata
                for sample in samples:
                    sample['metadata'].update({
                        'repo_name': repo['name'],
                        'repo_stars': repo['stars'],
                        'repo_language': repo['language'],
                        'repo_license': repo['license']
                    })
                
                return samples
                
            except Exception as e:
                print(f"Error cloning/processing {repo['name']}: {e}")
                return []
    
    def _save_dataset(self, samples: List[Dict]):
        """Save the dataset in different formats."""
        # Remove duplicates
        unique_samples = self._remove_duplicates(samples)
        print(f"Removed {len(samples) - len(unique_samples)} duplicate samples")
        
        # Create train/validation/test splits
        splits = self._create_splits(unique_samples)
        
        # Save splits
        for split_name, split_data in splits.items():
            output_file = self.output_dir / 'processed' / f'{split_name}.json'
            with open(output_file, 'w') as f:
                json.dump(split_data, f, indent=2)
            print(f"Saved {len(split_data)} samples to {output_file}")
    
    def _remove_duplicates(self, samples: List[Dict]) -> List[Dict]:
        """Remove duplicate samples based on code hash."""
        seen_hashes = set()
        unique_samples = []
        
        for sample in samples:
            code_hash = sample['metadata']['hash']
            if code_hash not in seen_hashes:
                seen_hashes.add(code_hash)
                unique_samples.append(sample)
        
        return unique_samples
    
    def _create_splits(self, samples: List[Dict]) -> Dict[str, List[Dict]]:
        """Create train/validation/test splits."""
        random.shuffle(samples)
        
        total = len(samples)
        train_size = int(total * 0.8)
        val_size = int(total * 0.1)
        
        return {
            'train': samples[:train_size],
            'validation': samples[train_size:train_size + val_size],
            'test': samples[train_size + val_size:]
        }
    
    def _generate_statistics(self, samples: List[Dict]):
        """Generate and save dataset statistics."""
        stats = {
            'total_samples': len(samples),
            'languages': {},
            'categories': {},
            'sample_types': {},
            'template_types': {},
            'avg_length': sum(len(s['output']) for s in samples) / len(samples) if samples else 0
        }
        
        for sample in samples:
            lang = sample['language']
            cat = sample['category']
            stype = sample['sample_type']
            ttype = sample['template_type']
            
            stats['languages'][lang] = stats['languages'].get(lang, 0) + 1
            stats['categories'][cat] = stats['categories'].get(cat, 0) + 1
            stats['sample_types'][stype] = stats['sample_types'].get(stype, 0) + 1
            stats['template_types'][ttype] = stats['template_types'].get(ttype, 0) + 1
        
        # Save statistics
        with open(self.output_dir / 'dataset_statistics.json', 'w') as f:
            json.dump(stats, f, indent=2)
        
        print("\nDataset Statistics:")
        print(f"Total samples: {stats['total_samples']}")
        print(f"Average code length: {stats['avg_length']:.1f} characters")
        print(f"Languages: {list(stats['languages'].keys())}")
        print(f"Categories: {list(stats['categories'].keys())}")

if __name__ == "__main__":
    # Example usage - collect 50K samples
    builder = DatasetBuilder()
    builder.build_dataset(target_samples=50000)
