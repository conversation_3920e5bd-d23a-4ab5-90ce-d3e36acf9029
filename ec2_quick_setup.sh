#!/bin/bash
# Quick Setup Script for EC2 Instance
# Run this script directly on your EC2 instance after manual creation

set -e

echo "=== Code Llama Training Environment Setup on EC2 ==="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on EC2
check_ec2() {
    if ! curl -s --max-time 5 http://***************/latest/meta-data/instance-id > /dev/null; then
        print_error "This script should be run on an EC2 instance"
        exit 1
    fi
    print_status "Running on EC2 instance: $(curl -s http://***************/latest/meta-data/instance-id)"
}

# Setup additional storage
setup_storage() {
    print_status "Setting up additional storage..."
    
    # Check if /data already exists and is mounted
    if mountpoint -q /data; then
        print_status "/data is already mounted"
        return
    fi
    
    # Find the additional EBS volume
    DEVICE=$(lsblk -f | grep -E "nvme[0-9]+n1" | grep -v "part" | tail -1 | awk '{print "/dev/"$1}')
    
    if [ -z "$DEVICE" ]; then
        print_warning "No additional EBS volume found. Using /home/<USER>/data"
        mkdir -p /home/<USER>/data
        sudo ln -sf /home/<USER>/data /data
        sudo chown -R ubuntu:ubuntu /home/<USER>/data
    else
        print_status "Found additional volume: $DEVICE"
        
        # Format if not already formatted
        if ! blkid $DEVICE; then
            print_status "Formatting $DEVICE..."
            sudo mkfs.ext4 $DEVICE
        fi
        
        # Create mount point and mount
        sudo mkdir -p /data
        sudo mount $DEVICE /data
        
        # Add to fstab for persistent mounting
        if ! grep -q "$DEVICE" /etc/fstab; then
            echo "$DEVICE /data ext4 defaults 0 0" | sudo tee -a /etc/fstab
        fi
        
        # Set ownership
        sudo chown -R ubuntu:ubuntu /data
    fi
    
    print_status "Storage setup complete. Available space:"
    df -h /data
}

# Install system packages
install_system_packages() {
    print_status "Installing system packages..."
    
    sudo apt update
    sudo apt install -y git wget curl htop nvtop screen tmux tree unzip python3-pip
    
    print_status "System packages installed"
}

# Setup Python environment
setup_python_env() {
    print_status "Setting up Python environment..."
    
    # Check if conda is already available (Deep Learning AMI)
    if command -v conda &> /dev/null; then
        print_status "Conda already available"
    else
        print_status "Installing Miniconda..."
        cd /home/<USER>
        wget -q https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
        bash Miniconda3-latest-Linux-x86_64.sh -b -p /home/<USER>/miniconda3
        echo 'export PATH="/home/<USER>/miniconda3/bin:$PATH"' >> ~/.bashrc
        export PATH="/home/<USER>/miniconda3/bin:$PATH"
    fi
    
    # Create training environment
    print_status "Creating llama-training environment..."
    conda create -n llama-training python=3.10 -y
    
    print_status "Python environment setup complete"
}

# Install Python packages
install_python_packages() {
    print_status "Installing Python packages..."
    
    # Activate environment
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate llama-training
    
    # Install PyTorch with CUDA
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    
    # Install training dependencies
    pip install transformers datasets accelerate bitsandbytes peft trl
    pip install wandb tensorboard jupyter
    pip install PyYAML gitpython PyGithub requests
    pip install nvidia-ml-py3 psutil tqdm
    
    print_status "Python packages installed"
}

# Setup project structure
setup_project() {
    print_status "Setting up project structure..."
    
    # Create project directory
    mkdir -p /data/codellama-training
    cd /data/codellama-training
    
    # Create subdirectories
    mkdir -p {dataset/{raw_data,processed},models,logs,scripts,configs}
    
    print_status "Project structure created at /data/codellama-training"
}

# Create utility scripts
create_utilities() {
    print_status "Creating utility scripts..."
    
    cd /data/codellama-training
    
    # Monitor script
    cat > monitor.sh << 'EOF'
#!/bin/bash
echo "=== System Status ==="
echo "Instance: $(curl -s http://***************/latest/meta-data/instance-id)"
echo "Public IP: $(curl -s http://***************/latest/meta-data/public-ipv4)"
echo "Time: $(date)"

echo -e "\n=== GPU Status ==="
nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu --format=csv,noheader

echo -e "\n=== Disk Usage ==="
df -h /data

echo -e "\n=== Memory Usage ==="
free -h

echo -e "\n=== Training Processes ==="
ps aux | grep python | grep -v grep | head -5
EOF
    
    # Start services script
    cat > start_services.sh << 'EOF'
#!/bin/bash
cd /data/codellama-training
source $(conda info --base)/etc/profile.d/conda.sh
conda activate llama-training

# Kill existing services
pkill -f tensorboard
pkill -f jupyter

# Start TensorBoard
nohup tensorboard --logdir=./logs --host=0.0.0.0 --port=6006 > tensorboard.log 2>&1 &

# Start Jupyter
nohup jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root > jupyter.log 2>&1 &

sleep 3

PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
echo "Services started!"
echo "TensorBoard: http://$PUBLIC_IP:6006"
echo "Jupyter: http://$PUBLIC_IP:8888"
echo "Logs: tensorboard.log, jupyter.log"
EOF
    
    # Environment activation script
    cat > activate_env.sh << 'EOF'
#!/bin/bash
source $(conda info --base)/etc/profile.d/conda.sh
conda activate llama-training
cd /data/codellama-training
echo "Environment activated. Current directory: $(pwd)"
echo "Python: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
EOF
    
    # Make scripts executable
    chmod +x *.sh
    
    print_status "Utility scripts created"
}

# Configure Jupyter
configure_jupyter() {
    print_status "Configuring Jupyter..."
    
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate llama-training
    
    # Generate config if it doesn't exist
    jupyter notebook --generate-config
    
    # Configure for remote access
    cat >> ~/.jupyter/jupyter_notebook_config.py << 'EOF'
c.NotebookApp.ip = '0.0.0.0'
c.NotebookApp.open_browser = False
c.NotebookApp.port = 8888
c.NotebookApp.allow_root = True
c.NotebookApp.token = ''
c.NotebookApp.password = ''
EOF
    
    print_status "Jupyter configured"
}

# Create environment variables template
create_env_template() {
    print_status "Creating environment variables template..."
    
    cat > /data/codellama-training/set_env_vars.sh << 'EOF'
#!/bin/bash
# Set your API keys here

# GitHub Personal Access Token (required for dataset collection)
export GITHUB_TOKEN="your_github_token_here"

# Weights & Biases API Key (optional, for training monitoring)
export WANDB_API_KEY="your_wandb_key_here"

# Hugging Face Token (optional, for model downloads)
export HF_TOKEN="your_hf_token_here"

echo "Environment variables set!"
echo "Remember to edit this file with your actual API keys"
EOF
    
    chmod +x /data/codellama-training/set_env_vars.sh
    
    print_status "Environment template created at /data/codellama-training/set_env_vars.sh"
}

# Main setup function
main() {
    print_status "Starting EC2 setup for Code Llama training..."
    
    check_ec2
    setup_storage
    install_system_packages
    setup_python_env
    install_python_packages
    setup_project
    create_utilities
    configure_jupyter
    create_env_template
    
    print_status "Setup completed successfully!"
    
    echo ""
    echo "=== Next Steps ==="
    echo "1. Edit API keys: nano /data/codellama-training/set_env_vars.sh"
    echo "2. Set environment: source /data/codellama-training/set_env_vars.sh"
    echo "3. Activate environment: source /data/codellama-training/activate_env.sh"
    echo "4. Start services: ./start_services.sh"
    echo "5. Copy your training scripts to /data/codellama-training/"
    echo "6. Run dataset collection: python dataset_collector.py"
    echo "7. Start training: python train_code_llama.py"
    echo ""
    echo "=== Useful Commands ==="
    echo "Monitor system: ./monitor.sh"
    echo "Check logs: tail -f *.log"
    echo "Connect to screen: screen -S training"
    echo ""
    
    PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
    echo "=== Access URLs (after starting services) ==="
    echo "TensorBoard: http://$PUBLIC_IP:6006"
    echo "Jupyter: http://$PUBLIC_IP:8888"
}

# Run main function
main "$@"
