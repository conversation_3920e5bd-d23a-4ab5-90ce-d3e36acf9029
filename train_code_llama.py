#!/usr/bin/env python3
"""
Code Llama 7B Fine-tuning Script
Optimized for AWS g4dn instances with memory-efficient training using LoRA and 4-bit quantization.
"""

import os
import json
import torch
import wandb
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, field

import transformers
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from datasets import Dataset, load_dataset
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
import bitsandbytes as bnb

@dataclass
class ModelArguments:
    """Arguments for model configuration."""
    model_name_or_path: str = field(default="codellama/CodeLlama-7b-hf")
    use_4bit: bool = field(default=True)
    bnb_4bit_compute_dtype: str = field(default="float16")
    bnb_4bit_quant_type: str = field(default="nf4")
    use_nested_quant: bool = field(default=True)

@dataclass
class DataArguments:
    """Arguments for data configuration."""
    dataset_path: str = field(default="./dataset/processed")
    max_length: int = field(default=2048)
    train_file: str = field(default="train.json")
    validation_file: str = field(default="validation.json")

@dataclass
class LoraArguments:
    """Arguments for LoRA configuration."""
    lora_r: int = field(default=16)
    lora_alpha: int = field(default=32)
    lora_dropout: float = field(default=0.1)
    lora_target_modules: List[str] = field(default_factory=lambda: ["q_proj", "v_proj", "k_proj", "o_proj"])

class CodeLlamaTrainer:
    """Main trainer class for Code Llama fine-tuning."""
    
    def __init__(self, model_args: ModelArguments, data_args: DataArguments, 
                 lora_args: LoraArguments, training_args: TrainingArguments):
        self.model_args = model_args
        self.data_args = data_args
        self.lora_args = lora_args
        self.training_args = training_args
        
        # Initialize components
        self.tokenizer = None
        self.model = None
        self.train_dataset = None
        self.eval_dataset = None
        
        # Setup logging
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging and monitoring."""
        # Initialize Weights & Biases
        if self.training_args.report_to == ["wandb"]:
            wandb.init(
                project="code-llama-finetuning",
                name=f"codellama-7b-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
                config={
                    **vars(self.model_args),
                    **vars(self.data_args),
                    **vars(self.lora_args),
                    **vars(self.training_args)
                }
            )
    
    def load_tokenizer(self):
        """Load and configure tokenizer."""
        print("Loading tokenizer...")
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_args.model_name_or_path,
            trust_remote_code=True,
            padding_side="right"
        )
        
        # Add pad token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
        
        print(f"Tokenizer loaded. Vocab size: {len(self.tokenizer)}")
    
    def load_model(self):
        """Load and configure model with quantization and LoRA."""
        print("Loading model...")
        
        # Configure quantization
        if self.model_args.use_4bit:
            compute_dtype = getattr(torch, self.model_args.bnb_4bit_compute_dtype)
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_quant_type=self.model_args.bnb_4bit_quant_type,
                bnb_4bit_compute_dtype=compute_dtype,
                bnb_4bit_use_double_quant=self.model_args.use_nested_quant,
            )
        else:
            bnb_config = None
        
        # Load model
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_args.model_name_or_path,
            quantization_config=bnb_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16 if self.model_args.use_4bit else torch.float32,
        )
        
        # Prepare model for k-bit training
        if self.model_args.use_4bit:
            self.model = prepare_model_for_kbit_training(self.model)
        
        # Configure LoRA
        lora_config = LoraConfig(
            r=self.lora_args.lora_r,
            lora_alpha=self.lora_args.lora_alpha,
            target_modules=self.lora_args.lora_target_modules,
            lora_dropout=self.lora_args.lora_dropout,
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )
        
        # Apply LoRA
        self.model = get_peft_model(self.model, lora_config)
        self.model.print_trainable_parameters()
        
        print("Model loaded and configured with LoRA")
    
    def load_datasets(self):
        """Load and preprocess training datasets."""
        print("Loading datasets...")
        
        # Load train and validation datasets
        train_path = Path(self.data_args.dataset_path) / self.data_args.train_file
        val_path = Path(self.data_args.dataset_path) / self.data_args.validation_file
        
        if not train_path.exists():
            raise FileNotFoundError(f"Training file not found: {train_path}")
        if not val_path.exists():
            raise FileNotFoundError(f"Validation file not found: {val_path}")
        
        # Load JSON datasets
        with open(train_path, 'r') as f:
            train_data = json.load(f)
        with open(val_path, 'r') as f:
            val_data = json.load(f)
        
        # Convert to HuggingFace datasets
        self.train_dataset = Dataset.from_list(train_data)
        self.eval_dataset = Dataset.from_list(val_data)
        
        # Preprocess datasets
        self.train_dataset = self.train_dataset.map(
            self.preprocess_function,
            batched=True,
            remove_columns=self.train_dataset.column_names
        )
        
        self.eval_dataset = self.eval_dataset.map(
            self.preprocess_function,
            batched=True,
            remove_columns=self.eval_dataset.column_names
        )
        
        print(f"Loaded {len(self.train_dataset)} training samples")
        print(f"Loaded {len(self.eval_dataset)} validation samples")
    
    def preprocess_function(self, examples):
        """Preprocess examples for training."""
        # Format examples as instruction-following
        formatted_examples = []
        
        for i in range(len(examples['instruction'])):
            # Create instruction-following format
            if examples['input'][i]:
                text = f"### Instruction:\n{examples['instruction'][i]}\n\n### Input:\n{examples['input'][i]}\n\n### Response:\n{examples['output'][i]}"
            else:
                text = f"### Instruction:\n{examples['instruction'][i]}\n\n### Response:\n{examples['output'][i]}"
            
            formatted_examples.append(text)
        
        # Tokenize
        tokenized = self.tokenizer(
            formatted_examples,
            truncation=True,
            padding=False,
            max_length=self.data_args.max_length,
            return_tensors=None
        )
        
        # Set labels for causal language modeling
        tokenized["labels"] = tokenized["input_ids"].copy()
        
        return tokenized
    
    def train(self):
        """Run the training process."""
        print("Starting training...")
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,
            pad_to_multiple_of=8
        )
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=self.training_args,
            train_dataset=self.train_dataset,
            eval_dataset=self.eval_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator,
        )
        
        # Add custom callbacks
        trainer.add_callback(self.SaveCheckpointCallback())
        
        # Start training
        trainer.train()
        
        # Save final model
        trainer.save_model()
        self.tokenizer.save_pretrained(self.training_args.output_dir)
        
        print("Training completed!")
    
    class SaveCheckpointCallback(transformers.TrainerCallback):
        """Custom callback for saving checkpoints to S3."""
        
        def on_save(self, args, state, control, **kwargs):
            """Save checkpoint to S3 if configured."""
            if os.getenv('S3_CHECKPOINT_BUCKET'):
                checkpoint_dir = f"{args.output_dir}/checkpoint-{state.global_step}"
                s3_path = f"s3://{os.getenv('S3_CHECKPOINT_BUCKET')}/checkpoints/"
                
                # Upload to S3 (requires AWS CLI)
                os.system(f"aws s3 sync {checkpoint_dir} {s3_path}checkpoint-{state.global_step}/")
                print(f"Checkpoint uploaded to {s3_path}")

def main():
    """Main training function."""
    # Parse arguments
    parser = transformers.HfArgumentParser((ModelArguments, DataArguments, LoraArguments, TrainingArguments))
    model_args, data_args, lora_args, training_args = parser.parse_args_into_dataclasses()
    
    # Set default training arguments if not provided
    if not hasattr(training_args, 'output_dir') or training_args.output_dir is None:
        training_args.output_dir = f"./models/codellama-7b-finetuned-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
    
    # Create trainer
    trainer = CodeLlamaTrainer(model_args, data_args, lora_args, training_args)
    
    # Load components
    trainer.load_tokenizer()
    trainer.load_model()
    trainer.load_datasets()
    
    # Start training
    trainer.train()

if __name__ == "__main__":
    main()
