#!/usr/bin/env python3
"""
Code Llama Deployment Options
Demonstrates both runtime combination and merged model approaches
"""

import torch
import argparse
import time
import os
from pathlib import Path
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel

class CodeLlamaDeployer:
    def __init__(self):
        self.device = "mps" if torch.backends.mps.is_available() else "cpu"
        print(f"Using device: {self.device}")
    
    def print_memory_usage(self):
        """Print current memory usage"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            print(f"Memory usage: {memory.used/1e9:.1f}GB / {memory.total/1e9:.1f}GB")
            print(f"Available: {memory.available/1e9:.1f}GB")
        except ImportError:
            print("Install psutil to see memory usage: pip install psutil")
    
    def load_runtime_combination(self, base_model_path, adapter_path):
        """Load model with runtime adapter combination"""
        print("\n=== Runtime Combination Approach ===")
        print("Loading base model...")
        start_time = time.time()
        
        # Load base model with 4-bit quantization
        model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            device_map="auto",
            torch_dtype=torch.float16,
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            low_cpu_mem_usage=True
        )
        
        print("Loading LoRA adapter...")
        model = PeftModel.from_pretrained(model, adapter_path)
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(base_model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        loading_time = time.time() - start_time
        print(f"Model loaded in {loading_time:.1f} seconds")
        self.print_memory_usage()
        
        return model, tokenizer
    
    def merge_adapter(self, base_model_path, adapter_path, output_path):
        """Merge LoRA adapter with base model"""
        print("\n=== Merging Adapter with Base Model ===")
        
        if os.path.exists(output_path):
            print(f"Merged model already exists at {output_path}")
            return output_path
        
        print("Loading base model...")
        model = AutoModelForCausalLM.from_pretrained(
            base_model_path,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        print("Loading LoRA adapter...")
        model = PeftModel.from_pretrained(model, adapter_path)
        
        print("Merging adapter with base model...")
        model = model.merge_and_unload()
        
        print(f"Saving merged model to {output_path}...")
        os.makedirs(output_path, exist_ok=True)
        model.save_pretrained(output_path)
        
        # Also save tokenizer
        tokenizer = AutoTokenizer.from_pretrained(base_model_path)
        tokenizer.save_pretrained(output_path)
        
        print("Merge completed!")
        return output_path
    
    def load_merged_model(self, merged_model_path):
        """Load pre-merged model"""
        print("\n=== Merged Model Approach ===")
        print("Loading merged model...")
        start_time = time.time()
        
        # Load merged model with 4-bit quantization
        model = AutoModelForCausalLM.from_pretrained(
            merged_model_path,
            device_map="auto",
            torch_dtype=torch.float16,
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            low_cpu_mem_usage=True
        )
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(merged_model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        loading_time = time.time() - start_time
        print(f"Model loaded in {loading_time:.1f} seconds")
        self.print_memory_usage()
        
        return model, tokenizer
    
    def generate_code(self, model, tokenizer, prompt, max_length=300, temperature=0.7):
        """Generate code from prompt"""
        # Format prompt
        formatted_prompt = f"### Instruction:\n{prompt}\n\n### Response:\n"
        
        # Tokenize
        inputs = tokenizer(
            formatted_prompt,
            return_tensors="pt",
            truncation=True,
            max_length=1024
        )
        
        # Generate
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_length=inputs.input_ids.shape[1] + max_length,
                temperature=temperature,
                do_sample=True,
                top_p=0.9,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                use_cache=True
            )
        
        # Decode response
        response = tokenizer.decode(
            outputs[0][inputs.input_ids.shape[1]:],
            skip_special_tokens=True
        )
        
        generation_time = time.time() - start_time
        tokens_generated = len(outputs[0]) - len(inputs.input_ids[0])
        
        print(f"Generated {tokens_generated} tokens in {generation_time:.2f}s")
        print(f"Speed: {tokens_generated/generation_time:.1f} tokens/second")
        
        return response.strip()
    
    def compare_approaches(self, base_model_path, adapter_path, test_prompt):
        """Compare both deployment approaches"""
        print("=" * 60)
        print("COMPARING DEPLOYMENT APPROACHES")
        print("=" * 60)
        
        # Test runtime combination
        print("\n1. Testing Runtime Combination...")
        try:
            model_runtime, tokenizer_runtime = self.load_runtime_combination(
                base_model_path, adapter_path
            )
            
            print(f"\nGenerating code for: {test_prompt}")
            response_runtime = self.generate_code(
                model_runtime, tokenizer_runtime, test_prompt
            )
            print(f"Response:\n{response_runtime}")
            
            # Clean up
            del model_runtime, tokenizer_runtime
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
        except Exception as e:
            print(f"Runtime combination failed: {e}")
            response_runtime = None
        
        # Test merged model
        print("\n" + "="*60)
        print("2. Testing Merged Model...")
        try:
            merged_path = "./codellama-7b-merged"
            
            # Merge if not exists
            self.merge_adapter(base_model_path, adapter_path, merged_path)
            
            # Load merged model
            model_merged, tokenizer_merged = self.load_merged_model(merged_path)
            
            print(f"\nGenerating code for: {test_prompt}")
            response_merged = self.generate_code(
                model_merged, tokenizer_merged, test_prompt
            )
            print(f"Response:\n{response_merged}")
            
            # Clean up
            del model_merged, tokenizer_merged
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
        except Exception as e:
            print(f"Merged model failed: {e}")
            response_merged = None
        
        # Summary
        print("\n" + "="*60)
        print("SUMMARY")
        print("="*60)
        print("Both approaches should produce similar results.")
        print("Choose based on your needs:")
        print("- Runtime Combination: More flexible, can swap adapters")
        print("- Merged Model: Faster loading, easier distribution")

def main():
    parser = argparse.ArgumentParser(description="Code Llama Deployment Options")
    parser.add_argument(
        "--approach", 
        choices=["runtime", "merged", "compare"], 
        default="compare",
        help="Deployment approach to use"
    )
    parser.add_argument(
        "--base-model", 
        default="codellama/CodeLlama-7b-hf",
        help="Base model path"
    )
    parser.add_argument(
        "--adapter-path", 
        default="./codellama-7b-finetuned",
        help="LoRA adapter path"
    )
    parser.add_argument(
        "--prompt", 
        default="Write a Python function to calculate fibonacci numbers",
        help="Test prompt for code generation"
    )
    
    args = parser.parse_args()
    
    # Check if adapter exists
    if not os.path.exists(args.adapter_path):
        print(f"Error: Adapter path not found: {args.adapter_path}")
        print("Make sure you've downloaded your trained model first.")
        return
    
    deployer = CodeLlamaDeployer()
    
    if args.approach == "runtime":
        print("Using Runtime Combination approach...")
        model, tokenizer = deployer.load_runtime_combination(
            args.base_model, args.adapter_path
        )
        response = deployer.generate_code(model, tokenizer, args.prompt)
        print(f"\nPrompt: {args.prompt}")
        print(f"Response:\n{response}")
        
    elif args.approach == "merged":
        print("Using Merged Model approach...")
        merged_path = "./codellama-7b-merged"
        deployer.merge_adapter(args.base_model, args.adapter_path, merged_path)
        model, tokenizer = deployer.load_merged_model(merged_path)
        response = deployer.generate_code(model, tokenizer, args.prompt)
        print(f"\nPrompt: {args.prompt}")
        print(f"Response:\n{response}")
        
    elif args.approach == "compare":
        print("Comparing both approaches...")
        deployer.compare_approaches(args.base_model, args.adapter_path, args.prompt)

if __name__ == "__main__":
    main()
