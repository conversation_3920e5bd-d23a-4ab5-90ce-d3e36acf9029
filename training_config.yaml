# Code Llama 7B Fine-tuning Configuration
# Optimized for AWS g4dn.xlarge instances

# Model Configuration
model:
  name_or_path: "codellama/CodeLlama-7b-hf"
  use_4bit: true
  bnb_4bit_compute_dtype: "float16"
  bnb_4bit_quant_type: "nf4"
  use_nested_quant: true

# Data Configuration
data:
  dataset_path: "./dataset/processed"
  max_length: 2048
  train_file: "train.json"
  validation_file: "validation.json"
  test_file: "test.json"

# LoRA Configuration
lora:
  r: 16
  alpha: 32
  dropout: 0.1
  target_modules:
    - "q_proj"
    - "v_proj"
    - "k_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# Training Arguments
training:
  output_dir: "./models/codellama-7b-finetuned"
  overwrite_output_dir: true
  
  # Training hyperparameters
  num_train_epochs: 3
  per_device_train_batch_size: 4
  per_device_eval_batch_size: 4
  gradient_accumulation_steps: 4
  learning_rate: 2.0e-4
  weight_decay: 0.01
  warmup_steps: 100
  max_grad_norm: 1.0
  
  # Optimization
  optim: "paged_adamw_32bit"
  lr_scheduler_type: "cosine"
  
  # Memory optimization
  dataloader_pin_memory: false
  gradient_checkpointing: true
  fp16: true
  
  # Logging and evaluation
  logging_steps: 10
  eval_steps: 500
  save_steps: 500
  evaluation_strategy: "steps"
  save_strategy: "steps"
  save_total_limit: 3
  
  # Monitoring
  report_to: ["wandb", "tensorboard"]
  logging_dir: "./logs"
  
  # Early stopping
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  
  # Reproducibility
  seed: 42
  data_seed: 42

# AWS Configuration
aws:
  region: "us-east-1"
  instance_type: "g4dn.xlarge"
  spot_instance: true
  s3_checkpoint_bucket: "your-checkpoint-bucket"
  s3_model_bucket: "your-model-bucket"

# Monitoring Configuration
monitoring:
  wandb:
    project: "code-llama-finetuning"
    entity: "your-wandb-entity"
  
  tensorboard:
    log_dir: "./logs/tensorboard"
  
  metrics:
    - "train_loss"
    - "eval_loss"
    - "learning_rate"
    - "gpu_memory_usage"
    - "training_speed"

# Deployment Configuration
deployment:
  quantization:
    method: "dynamic"  # dynamic, static, qat
    bits: 8
  
  optimization:
    onnx_export: true
    tensorrt_optimization: false
    
  target_platforms:
    - "cpu"
    - "gpu"
    - "mobile"
