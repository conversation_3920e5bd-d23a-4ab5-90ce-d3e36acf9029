#!/bin/bash
# AWS Training Environment Setup Script for Code Llama 7B Fine-tuning
# Optimized for g4dn.xlarge instances with cost optimization

set -e

echo "=== Code Llama 7B Training Environment Setup ==="

# Configuration
INSTANCE_TYPE="g4dn.xlarge"
REGION="us-east-1"
AMI_ID="ami-0c02fb55956c7d316"  # Deep Learning AMI (Ubuntu 20.04)
KEY_NAME="your-key-pair"
SECURITY_GROUP="sg-your-security-group"
SUBNET_ID="subnet-your-subnet"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI not found. Please install AWS CLI first."
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials not configured. Run 'aws configure' first."
        exit 1
    fi
    
    # Check if key pair exists
    if ! aws ec2 describe-key-pairs --key-names "$KEY_NAME" --region "$REGION" &> /dev/null; then
        print_warning "Key pair '$KEY_NAME' not found. Creating new key pair..."
        aws ec2 create-key-pair --key-name "$KEY_NAME" --region "$REGION" --query 'KeyMaterial' --output text > "${KEY_NAME}.pem"
        chmod 400 "${KEY_NAME}.pem"
        print_status "Key pair created and saved as ${KEY_NAME}.pem"
    fi
    
    print_status "Prerequisites check completed."
}

# Create security group
create_security_group() {
    print_status "Creating security group..."
    
    # Create security group
    SECURITY_GROUP_ID=$(aws ec2 create-security-group \
        --group-name "codellama-training-sg" \
        --description "Security group for Code Llama training" \
        --region "$REGION" \
        --query 'GroupId' --output text 2>/dev/null || echo "exists")
    
    if [ "$SECURITY_GROUP_ID" != "exists" ]; then
        # Add SSH access
        aws ec2 authorize-security-group-ingress \
            --group-id "$SECURITY_GROUP_ID" \
            --protocol tcp \
            --port 22 \
            --cidr 0.0.0.0/0 \
            --region "$REGION"
        
        # Add Jupyter access
        aws ec2 authorize-security-group-ingress \
            --group-id "$SECURITY_GROUP_ID" \
            --protocol tcp \
            --port 8888 \
            --cidr 0.0.0.0/0 \
            --region "$REGION"
        
        # Add TensorBoard access
        aws ec2 authorize-security-group-ingress \
            --group-id "$SECURITY_GROUP_ID" \
            --protocol tcp \
            --port 6006 \
            --cidr 0.0.0.0/0 \
            --region "$REGION"
        
        print_status "Security group created: $SECURITY_GROUP_ID"
        SECURITY_GROUP="$SECURITY_GROUP_ID"
    else
        print_status "Security group already exists."
    fi
}

# Launch spot instance
launch_spot_instance() {
    print_status "Launching spot instance..."
    
    # Create launch template
    cat > launch-template.json << EOF
{
    "ImageId": "$AMI_ID",
    "InstanceType": "$INSTANCE_TYPE",
    "KeyName": "$KEY_NAME",
    "SecurityGroupIds": ["$SECURITY_GROUP"],
    "IamInstanceProfile": {
        "Name": "EC2-S3-Access-Role"
    },
    "BlockDeviceMappings": [
        {
            "DeviceName": "/dev/sda1",
            "Ebs": {
                "VolumeSize": 50,
                "VolumeType": "gp3",
                "DeleteOnTermination": true
            }
        },
        {
            "DeviceName": "/dev/sdf",
            "Ebs": {
                "VolumeSize": 500,
                "VolumeType": "gp3",
                "DeleteOnTermination": true
            }
        }
    ],
    "UserData": "$(base64 -w 0 user-data.sh)"
}
EOF
    
    # Request spot instance
    SPOT_REQUEST_ID=$(aws ec2 request-spot-instances \
        --spot-price "0.20" \
        --instance-count 1 \
        --type "one-time" \
        --launch-specification file://launch-template.json \
        --region "$REGION" \
        --query 'SpotInstanceRequests[0].SpotInstanceRequestId' \
        --output text)
    
    print_status "Spot instance requested: $SPOT_REQUEST_ID"
    
    # Wait for instance to be running
    print_status "Waiting for instance to be running..."
    aws ec2 wait spot-instance-request-fulfilled \
        --spot-instance-request-ids "$SPOT_REQUEST_ID" \
        --region "$REGION"
    
    # Get instance ID
    INSTANCE_ID=$(aws ec2 describe-spot-instance-requests \
        --spot-instance-request-ids "$SPOT_REQUEST_ID" \
        --region "$REGION" \
        --query 'SpotInstanceRequests[0].InstanceId' \
        --output text)
    
    print_status "Instance launched: $INSTANCE_ID"
    
    # Get public IP
    PUBLIC_IP=$(aws ec2 describe-instances \
        --instance-ids "$INSTANCE_ID" \
        --region "$REGION" \
        --query 'Reservations[0].Instances[0].PublicIpAddress' \
        --output text)
    
    print_status "Instance public IP: $PUBLIC_IP"
    
    # Save instance info
    cat > instance-info.txt << EOF
Instance ID: $INSTANCE_ID
Public IP: $PUBLIC_IP
Key Pair: $KEY_NAME
Security Group: $SECURITY_GROUP
Region: $REGION
SSH Command: ssh -i ${KEY_NAME}.pem ubuntu@$PUBLIC_IP
EOF
    
    print_status "Instance information saved to instance-info.txt"
}

# Create user data script
create_user_data() {
    print_status "Creating user data script..."
    
    cat > user-data.sh << 'EOF'
#!/bin/bash
# User data script for Code Llama training instance

# Update system
apt-get update
apt-get upgrade -y

# Install essential packages
apt-get install -y git wget curl htop nvtop screen tmux tree

# Mount additional EBS volume
mkfs.ext4 /dev/nvme1n1
mkdir -p /data
mount /dev/nvme1n1 /data
echo '/dev/nvme1n1 /data ext4 defaults 0 0' >> /etc/fstab
chown ubuntu:ubuntu /data

# Install Miniconda
cd /home/<USER>
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -b -p /home/<USER>/miniconda3
chown -R ubuntu:ubuntu /home/<USER>/miniconda3

# Setup environment for ubuntu user
sudo -u ubuntu bash << 'USEREOF'
cd /home/<USER>
echo 'export PATH="/home/<USER>/miniconda3/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Create conda environment
/home/<USER>/miniconda3/bin/conda create -n llama-training python=3.10 -y

# Activate environment and install packages
source /home/<USER>/miniconda3/bin/activate llama-training

# Install PyTorch with CUDA support
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install training dependencies
pip install transformers datasets accelerate bitsandbytes
pip install peft trl
pip install wandb tensorboard jupyter
pip install PyYAML gitpython

# Install additional utilities
pip install nvidia-ml-py3 psutil

# Create project directory
mkdir -p /data/codellama-training
cd /data/codellama-training

# Clone training repository (replace with your repo)
# git clone https://github.com/your-username/codellama-training.git .

# Setup Jupyter
jupyter notebook --generate-config
echo "c.NotebookApp.ip = '0.0.0.0'" >> ~/.jupyter/jupyter_notebook_config.py
echo "c.NotebookApp.open_browser = False" >> ~/.jupyter/jupyter_notebook_config.py
echo "c.NotebookApp.port = 8888" >> ~/.jupyter/jupyter_notebook_config.py

USEREOF

# Create startup script
cat > /home/<USER>/start-training.sh << 'STARTEOF'
#!/bin/bash
cd /data/codellama-training
source /home/<USER>/miniconda3/bin/activate llama-training

# Start TensorBoard
tensorboard --logdir=./logs --host=0.0.0.0 --port=6006 &

# Start Jupyter
jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser &

echo "Training environment ready!"
echo "TensorBoard: http://$(curl -s http://***************/latest/meta-data/public-ipv4):6006"
echo "Jupyter: http://$(curl -s http://***************/latest/meta-data/public-ipv4):8888"
STARTEOF

chmod +x /home/<USER>/start-training.sh
chown ubuntu:ubuntu /home/<USER>/start-training.sh

# Create monitoring script
cat > /home/<USER>/monitor-training.sh << 'MONEOF'
#!/bin/bash
# Training monitoring script

echo "=== GPU Status ==="
nvidia-smi

echo -e "\n=== Disk Usage ==="
df -h

echo -e "\n=== Memory Usage ==="
free -h

echo -e "\n=== Training Processes ==="
ps aux | grep python | grep -v grep

echo -e "\n=== GPU Memory Usage ==="
nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits
MONEOF

chmod +x /home/<USER>/monitor-training.sh
chown ubuntu:ubuntu /home/<USER>/monitor-training.sh

# Signal completion
touch /home/<USER>/setup-complete
EOF
}

# Create cost monitoring script
create_cost_monitor() {
    print_status "Creating cost monitoring script..."
    
    cat > monitor-costs.sh << 'EOF'
#!/bin/bash
# AWS Cost Monitoring Script

REGION="us-east-1"
START_DATE=$(date -d "1 month ago" +%Y-%m-%d)
END_DATE=$(date +%Y-%m-%d)

echo "=== AWS Cost Monitoring ==="
echo "Period: $START_DATE to $END_DATE"

# Get cost and usage
aws ce get-cost-and-usage \
    --time-period Start=$START_DATE,End=$END_DATE \
    --granularity MONTHLY \
    --metrics BlendedCost \
    --group-by Type=DIMENSION,Key=SERVICE \
    --region $REGION

# Get current month costs
CURRENT_MONTH_START=$(date +%Y-%m-01)
echo -e "\n=== Current Month Costs ==="
aws ce get-cost-and-usage \
    --time-period Start=$CURRENT_MONTH_START,End=$END_DATE \
    --granularity DAILY \
    --metrics BlendedCost \
    --region $REGION

# Get spot instance savings
echo -e "\n=== Spot Instance Savings ==="
aws ec2 describe-spot-price-history \
    --instance-types g4dn.xlarge \
    --product-descriptions "Linux/UNIX" \
    --max-items 1 \
    --region $REGION
EOF
    
    chmod +x monitor-costs.sh
}

# Main execution
main() {
    print_status "Starting AWS training environment setup..."
    
    check_prerequisites
    create_user_data
    create_security_group
    launch_spot_instance
    create_cost_monitor
    
    print_status "Setup completed successfully!"
    print_status "Instance information saved to instance-info.txt"
    print_status "Use 'bash monitor-costs.sh' to monitor AWS costs"
    
    echo -e "\n${GREEN}Next Steps:${NC}"
    echo "1. Wait for instance initialization (5-10 minutes)"
    echo "2. SSH to instance: ssh -i ${KEY_NAME}.pem ubuntu@$PUBLIC_IP"
    echo "3. Run setup check: ls -la /home/<USER>/setup-complete"
    echo "4. Start training environment: bash /home/<USER>/start-training.sh"
    echo "5. Upload your training data to /data/codellama-training/dataset/"
    echo "6. Start training: python train_code_llama.py"
}

# Run main function
main "$@"
