# File Transfer Guide for EC2 Setup

## Overview
This guide shows you how to get all the training scripts and configuration files onto your EC2 instance after manual creation.

## Method 1: Direct Copy-Paste (Recommended for Small Files)

### Step 1: Connect to EC2
```bash
ssh -i your-key.pem ubuntu@YOUR_EC2_PUBLIC_IP
```

### Step 2: Run Quick Setup
```bash
# Download and run the quick setup script
wget https://raw.githubusercontent.com/your-repo/main/ec2_quick_setup.sh
chmod +x ec2_quick_setup.sh
./ec2_quick_setup.sh
```

### Step 3: Create Training Files
Navigate to the project directory and create each file:

```bash
cd /data/codellama-training
source activate_env.sh
```

#### Create dataset_collector.py
```bash
nano dataset_collector.py
# Copy-paste the entire content from your local dataset_collector.py file
# Save with Ctrl+X, Y, Enter
```

#### Create train_code_llama.py
```bash
nano train_code_llama.py
# Copy-paste the entire content from your local train_code_llama.py file
# Save with Ctrl+X, Y, Enter
```

#### Create training_config.yaml
```bash
nano training_config.yaml
# Copy-paste the content from your local training_config.yaml file
# Save with Ctrl+X, Y, Enter
```

#### Create requirements.txt
```bash
nano requirements.txt
# Copy-paste the content from your local requirements.txt file
# Save with Ctrl+X, Y, Enter
```

## Method 2: Using SCP (From Your Local Machine)

### Step 1: Prepare Files Locally
Make sure all files are in one directory on your local machine:
```bash
# On your local machine
ls -la
# Should show: dataset_collector.py, train_code_llama.py, training_config.yaml, etc.
```

### Step 2: Transfer Files
```bash
# From your local machine
scp -i your-key.pem *.py *.yaml *.txt *.md ubuntu@YOUR_EC2_PUBLIC_IP:/data/codellama-training/
```

## Method 3: Using GitHub (Recommended for Version Control)

### Step 1: Create GitHub Repository
1. Create a new repository on GitHub
2. Upload all your files to the repository
3. Make it public or ensure you have access

### Step 2: Clone on EC2
```bash
# On EC2 instance
cd /data/codellama-training
git clone https://github.com/YOUR_USERNAME/YOUR_REPO.git .
```

## Method 4: Using wget/curl for Individual Files

If you have files hosted somewhere (like GitHub raw URLs):

```bash
# On EC2 instance
cd /data/codellama-training

# Download each file
wget https://raw.githubusercontent.com/YOUR_USERNAME/YOUR_REPO/main/dataset_collector.py
wget https://raw.githubusercontent.com/YOUR_USERNAME/YOUR_REPO/main/train_code_llama.py
wget https://raw.githubusercontent.com/YOUR_USERNAME/YOUR_REPO/main/training_config.yaml
```

## Quick File Creation Templates

If you prefer to create files directly on EC2, here are the essential files:

### Essential File 1: requirements.txt
```bash
cat > requirements.txt << 'EOF'
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
accelerate>=0.24.0
peft>=0.6.0
trl>=0.7.0
bitsandbytes>=0.41.0
wandb>=0.15.0
tensorboard>=2.14.0
jupyter>=1.0.0
PyYAML>=6.0
PyGithub>=1.59.0
GitPython>=3.1.0
requests>=2.31.0
nvidia-ml-py3>=7.352.0
psutil>=5.9.0
tqdm>=4.66.0
EOF
```

### Essential File 2: Simple Training Config
```bash
cat > simple_config.yaml << 'EOF'
# Simple training configuration
model:
  name_or_path: "codellama/CodeLlama-7b-hf"
  use_4bit: true

data:
  dataset_path: "./dataset/processed"
  max_length: 1024

training:
  output_dir: "./models/codellama-7b-finetuned"
  num_train_epochs: 2
  per_device_train_batch_size: 2
  gradient_accumulation_steps: 8
  learning_rate: 2.0e-4
  logging_steps: 10
  save_steps: 500
  eval_steps: 500
  fp16: true
  gradient_checkpointing: true
  report_to: ["tensorboard"]

lora:
  r: 16
  alpha: 32
  dropout: 0.1
  target_modules: ["q_proj", "v_proj"]
EOF
```

### Essential File 3: Simple Dataset Collector
```bash
cat > simple_dataset_collector.py << 'EOF'
#!/usr/bin/env python3
"""
Simple dataset collector for Code Llama training
"""
import os
import json
import requests
from pathlib import Path

def collect_sample_data():
    """Create a small sample dataset for testing"""
    
    # Create directories
    Path("dataset/processed").mkdir(parents=True, exist_ok=True)
    
    # Sample training data
    sample_data = [
        {
            "instruction": "Write a Python function to calculate factorial",
            "input": "",
            "output": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n-1)",
            "language": "python",
            "category": "algorithms"
        },
        {
            "instruction": "Create a JavaScript function to reverse a string",
            "input": "",
            "output": "function reverseString(str) {\n    return str.split('').reverse().join('');\n}",
            "language": "javascript",
            "category": "string_manipulation"
        },
        # Add more samples as needed
    ]
    
    # Create train/val splits
    train_size = int(len(sample_data) * 0.8)
    train_data = sample_data[:train_size]
    val_data = sample_data[train_size:]
    
    # Save datasets
    with open("dataset/processed/train.json", "w") as f:
        json.dump(train_data, f, indent=2)
    
    with open("dataset/processed/validation.json", "w") as f:
        json.dump(val_data, f, indent=2)
    
    print(f"Created sample dataset with {len(train_data)} training and {len(val_data)} validation samples")

if __name__ == "__main__":
    collect_sample_data()
EOF
```

## Verification Steps

After transferring files, verify everything is set up correctly:

```bash
# On EC2 instance
cd /data/codellama-training
source activate_env.sh

# Check files are present
ls -la

# Test Python imports
python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}')"
python -c "import transformers; print('Transformers OK')"

# Test dataset collector
python simple_dataset_collector.py

# Check dataset was created
ls -la dataset/processed/

# Start services
./start_services.sh

# Check services are running
./monitor.sh
```

## Troubleshooting

### File Permission Issues
```bash
# Fix permissions
chmod +x *.sh
chmod 644 *.py *.yaml *.txt *.md
```

### Missing Dependencies
```bash
# Reinstall requirements
source activate_env.sh
pip install -r requirements.txt
```

### Storage Issues
```bash
# Check disk space
df -h
# Clean up if needed
sudo apt autoremove
conda clean --all
```

## Quick Start Commands

Once files are transferred:

```bash
# 1. Activate environment
source /data/codellama-training/activate_env.sh

# 2. Set API keys
nano set_env_vars.sh  # Edit with your keys
source set_env_vars.sh

# 3. Start monitoring services
./start_services.sh

# 4. Create sample dataset (for testing)
python simple_dataset_collector.py

# 5. Start training
python train_code_llama.py --config simple_config.yaml

# 6. Monitor progress
./monitor.sh
```

This approach gives you maximum flexibility to set up and run everything directly on your EC2 instance without needing complex local setup.
