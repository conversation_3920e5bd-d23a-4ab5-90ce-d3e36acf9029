# Model Accuracy and Download Information

## 🎯 Quantization Accuracy Comparison

### Real-World Performance Impact

| Quantization | Accuracy Retention | Code Quality | Chat Quality | Memory Usage |
|-------------|-------------------|--------------|--------------|--------------|
| **Original (FP16)** | 100% (baseline) | Excellent | Excellent | ~14GB |
| **8-bit** | **98-99%** | **Excellent** | **Excellent** | ~7GB |
| **4-bit** | **95-98%** | **Very Good** | **Very Good** | ~4GB |

### Detailed Performance Analysis

#### 8-bit Quantization
- **Code Generation**: 98-99% accuracy vs original
- **Code Completion**: Virtually identical performance
- **Bug Detection**: Minimal degradation
- **Code Explanation**: No noticeable difference
- **Multi-language Support**: Full retention
- **Recommended for**: Best balance of quality and efficiency

#### 4-bit Quantization
- **Code Generation**: 95-98% accuracy vs original
- **Code Completion**: Slight reduction in edge cases
- **Bug Detection**: Minor degradation in complex scenarios
- **Code Explanation**: Occasionally less detailed
- **Multi-language Support**: Good retention
- **Recommended for**: Memory-constrained environments

### Practical Examples

#### Code Generation Task
**Prompt**: "Write a Python function to merge two sorted lists"

**Original Model Output**:
```python
def merge_sorted_lists(list1, list2):
    """
    Merge two sorted lists into a single sorted list.
    
    Args:
        list1: First sorted list
        list2: Second sorted list
    
    Returns:
        Merged sorted list
    """
    merged = []
    i, j = 0, 0
    
    while i < len(list1) and j < len(list2):
        if list1[i] <= list2[j]:
            merged.append(list1[i])
            i += 1
        else:
            merged.append(list2[j])
            j += 1
    
    merged.extend(list1[i:])
    merged.extend(list2[j:])
    
    return merged
```

**8-bit Quantized Output**: 
- ✅ Identical or near-identical code
- ✅ Same logic and structure
- ✅ Complete docstring

**4-bit Quantized Output**:
- ✅ Correct algorithm implementation
- ⚠️ Might have shorter docstring
- ⚠️ Variable names might be slightly different
- ✅ Functionally equivalent

#### Chat/Explanation Task
**Prompt**: "Explain what this code does: `list(map(lambda x: x**2, range(10)))`"

**8-bit vs Original**: 
- Nearly identical explanations
- Same level of detail
- Consistent technical accuracy

**4-bit vs Original**:
- Correct explanation but might be more concise
- Occasionally misses minor details
- Still technically accurate

## 📥 Original Model Download

### Hugging Face Hub (Recommended)
```bash
# Method 1: Using transformers library (automatic download)
python -c "
from transformers import AutoModelForCausalLM, AutoTokenizer
model = AutoModelForCausalLM.from_pretrained('codellama/CodeLlama-7b-hf')
tokenizer = AutoTokenizer.from_pretrained('codellama/CodeLlama-7b-hf')
"

# Method 2: Using huggingface-hub
pip install huggingface-hub
huggingface-cli download codellama/CodeLlama-7b-hf

# Method 3: Git LFS (manual download)
git lfs install
git clone https://huggingface.co/codellama/CodeLlama-7b-hf
```

### Direct Links
- **Model Page**: https://huggingface.co/codellama/CodeLlama-7b-hf
- **Files**: https://huggingface.co/codellama/CodeLlama-7b-hf/tree/main
- **Model Size**: ~13.5GB
- **License**: Custom (Commercial use allowed with restrictions)

### Alternative Models
```bash
# Code Llama variants
codellama/CodeLlama-7b-Python-hf     # Python-specialized
codellama/CodeLlama-7b-Instruct-hf   # Instruction-tuned

# Other code models
microsoft/CodeT5p-770M               # Smaller alternative
Salesforce/codegen-2B-mono           # 2B parameter option
```

## 📊 Current Dataset Configuration

### Target Dataset Size: 50,000 samples

#### Language Distribution (Approximate)
| Category | Languages | Target Samples | Percentage |
|----------|-----------|----------------|------------|
| **Web Backend** | Python, PHP, Java, JS, TS | 20,000 | 40% |
| **Web Frontend** | JavaScript, TypeScript, HTML, CSS | 10,000 | 20% |
| **Systems** | C, C++, Rust, Go, Assembly | 12,500 | 25% |
| **Mobile** | Kotlin, Swift, Dart, Java | 5,000 | 10% |
| **Game** | C#, C++, GDScript | 2,500 | 5% |

#### Sample Types Distribution
- **Function Completion**: 40% (~20,000 samples)
- **Code Explanation**: 25% (~12,500 samples)
- **Bug Fixing**: 20% (~10,000 samples)
- **Code Optimization**: 15% (~7,500 samples)

#### Quality Filters
- **Repository Stars**: >100
- **Recent Activity**: Updated within 2 years
- **License**: Permissive (MIT, Apache, BSD)
- **Code Length**: 50-1000 characters per sample
- **Duplicate Removal**: Hash-based deduplication

### Dataset Collection Command
```bash
# Collect exactly 50K samples
python dataset_collector.py

# Expected output structure:
# dataset/
# ├── raw_data/           # Repository metadata
# ├── processed/
# │   ├── train.json      # 40,000 samples (80%)
# │   ├── validation.json # 5,000 samples (10%)
# │   └── test.json       # 5,000 samples (10%)
# └── dataset_statistics.json
```

## ⚡ Performance Recommendations

### For Your M2 MacBook (16GB RAM)

#### Recommended Configuration
```python
# Optimal setup for M2 MacBook
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel

# Load 4-bit quantized model with LoRA
model = AutoModelForCausalLM.from_pretrained(
    "codellama/CodeLlama-7b-hf",
    device_map="auto",
    torch_dtype=torch.float16,
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_quant_type="nf4",
    low_cpu_mem_usage=True
)

# Load your fine-tuned LoRA adapter
model = PeftModel.from_pretrained(model, "./models/codellama-7b-finetuned")

# Expected performance:
# - Memory usage: ~4.2GB
# - Generation speed: 8-12 tokens/second
# - Quality: 95-98% of original
```

#### Memory Usage Breakdown
```
Base Model (4-bit):     ~3.8GB
LoRA Adapters:          ~0.2GB
Runtime Overhead:       ~0.2GB
Total:                  ~4.2GB
Available for macOS:    ~11.8GB
```

## 🎯 Quality vs Efficiency Trade-offs

### When to Use Each Configuration

#### 4-bit Quantization (Recommended for M2)
**Use when**:
- Memory is limited (≤8GB available)
- Speed is priority
- Good enough quality for most tasks

**Best for**:
- Code completion
- Simple code generation
- Interactive development

#### 8-bit Quantization
**Use when**:
- Memory is moderate (8-12GB available)
- Quality is more important than speed
- Complex reasoning tasks

**Best for**:
- Code review and analysis
- Complex algorithm generation
- Educational explanations

#### Original Model (FP16)
**Use when**:
- Memory is abundant (≥16GB available)
- Maximum quality required
- Production applications

**Best for**:
- Critical code generation
- Research applications
- Benchmark comparisons

## 📈 Expected Training Results

With 50K samples and your configuration:
- **Training Time**: 6-10 hours on g4dn.xlarge
- **Training Cost**: $0.95-$1.58 (spot pricing)
- **Final Model Quality**: 95-98% of full fine-tuning
- **Model Size**: ~4.2GB (base + LoRA adapters)

Your setup is optimized for the best balance of cost, speed, and quality! 🚀
