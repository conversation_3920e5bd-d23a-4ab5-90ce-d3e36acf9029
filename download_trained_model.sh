#!/bin/bash
# Automated Model Download Script
# Downloads your trained Code Llama model from EC2 to MacBook

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
EC2_IP=""
KEY_FILE=""
MODEL_NAME="codellama-7b-finetuned"
LOCAL_DIR="./models"

# Function to show usage
show_usage() {
    echo "Usage: $0 -i EC2_IP -k KEY_FILE [-m MODEL_NAME] [-d LOCAL_DIR]"
    echo ""
    echo "Options:"
    echo "  -i EC2_IP      Public IP address of your EC2 instance"
    echo "  -k KEY_FILE    Path to your EC2 key pair (.pem file)"
    echo "  -m MODEL_NAME  Model directory name (default: codellama-7b-finetuned)"
    echo "  -d LOCAL_DIR   Local directory to save model (default: ./models)"
    echo "  -h             Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 -i ************ -k ~/aws-keys/my-key.pem"
}

# Parse command line arguments
while getopts "i:k:m:d:h" opt; do
    case $opt in
        i) EC2_IP="$OPTARG" ;;
        k) KEY_FILE="$OPTARG" ;;
        m) MODEL_NAME="$OPTARG" ;;
        d) LOCAL_DIR="$OPTARG" ;;
        h) show_usage; exit 0 ;;
        *) show_usage; exit 1 ;;
    esac
done

# Validate required arguments
if [ -z "$EC2_IP" ] || [ -z "$KEY_FILE" ]; then
    print_error "EC2 IP and key file are required"
    show_usage
    exit 1
fi

# Validate key file exists
if [ ! -f "$KEY_FILE" ]; then
    print_error "Key file not found: $KEY_FILE"
    exit 1
fi

# Set correct permissions for key file
chmod 400 "$KEY_FILE"

print_status "Starting model download from EC2..."
print_status "EC2 IP: $EC2_IP"
print_status "Key file: $KEY_FILE"
print_status "Model name: $MODEL_NAME"
print_status "Local directory: $LOCAL_DIR"

# Create local directory
mkdir -p "$LOCAL_DIR"

# Remote model path
REMOTE_PATH="/data/codellama-training/models/$MODEL_NAME"
LOCAL_PATH="$LOCAL_DIR/$MODEL_NAME"

# Check if model exists on EC2
print_status "Checking if model exists on EC2..."
if ! ssh -i "$KEY_FILE" -o ConnectTimeout=10 ubuntu@"$EC2_IP" "[ -d '$REMOTE_PATH' ]"; then
    print_error "Model directory not found on EC2: $REMOTE_PATH"
    print_error "Make sure training has completed and the model was saved"
    exit 1
fi

# Get model size
print_status "Checking model size..."
MODEL_SIZE=$(ssh -i "$KEY_FILE" ubuntu@"$EC2_IP" "du -sh '$REMOTE_PATH' | cut -f1")
print_status "Model size: $MODEL_SIZE"

# Check available disk space
AVAILABLE_SPACE=$(df -h . | awk 'NR==2 {print $4}')
print_status "Available disk space: $AVAILABLE_SPACE"

# List model files
print_status "Model files to download:"
ssh -i "$KEY_FILE" ubuntu@"$EC2_IP" "ls -la '$REMOTE_PATH/'"

# Confirm download
echo ""
read -p "Do you want to proceed with the download? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Download cancelled"
    exit 0
fi

# Download model using rsync (with progress)
print_status "Downloading model files..."
rsync -avz --progress -e "ssh -i '$KEY_FILE'" \
    ubuntu@"$EC2_IP":"$REMOTE_PATH"/ \
    "$LOCAL_PATH"/

# Verify download
if [ $? -eq 0 ]; then
    print_status "Download completed successfully!"
    
    # Show downloaded files
    print_status "Downloaded files:"
    ls -la "$LOCAL_PATH/"
    
    # Check for essential files
    ESSENTIAL_FILES=("adapter_config.json" "adapter_model.safetensors")
    for file in "${ESSENTIAL_FILES[@]}"; do
        if [ -f "$LOCAL_PATH/$file" ]; then
            print_status "✓ $file found"
        else
            print_warning "✗ $file missing"
        fi
    done
    
    # Create deployment script
    print_status "Creating deployment script..."
    cat > "$LOCAL_DIR/deploy_${MODEL_NAME}.py" << EOF
#!/usr/bin/env python3
"""
Quick deployment script for $MODEL_NAME
"""
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel

def load_model():
    print("Loading base model...")
    model = AutoModelForCausalLM.from_pretrained(
        "codellama/CodeLlama-7b-hf",
        device_map="auto",
        torch_dtype=torch.float16,
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_quant_type="nf4",
        low_cpu_mem_usage=True
    )
    
    print("Loading LoRA adapter...")
    model = PeftModel.from_pretrained(model, "$LOCAL_PATH")
    
    tokenizer = AutoTokenizer.from_pretrained("codellama/CodeLlama-7b-hf")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print("Model loaded successfully!")
    return model, tokenizer

def generate_code(model, tokenizer, prompt, max_length=300):
    formatted_prompt = f"### Instruction:\\n{prompt}\\n\\n### Response:\\n"
    inputs = tokenizer(formatted_prompt, return_tensors="pt", truncation=True)
    
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_length=inputs.input_ids.shape[1] + max_length,
            temperature=0.7,
            do_sample=True,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id
        )
    
    response = tokenizer.decode(
        outputs[0][inputs.input_ids.shape[1]:],
        skip_special_tokens=True
    )
    return response.strip()

if __name__ == "__main__":
    model, tokenizer = load_model()
    
    # Test generation
    prompt = "Write a Python function to calculate fibonacci numbers"
    response = generate_code(model, tokenizer, prompt)
    
    print(f"\\nPrompt: {prompt}")
    print(f"Response:\\n{response}")
EOF
    
    chmod +x "$LOCAL_DIR/deploy_${MODEL_NAME}.py"
    
    print_status "Deployment script created: $LOCAL_DIR/deploy_${MODEL_NAME}.py"
    
    echo ""
    echo "=== Next Steps ==="
    echo "1. Install dependencies:"
    echo "   pip install torch transformers peft accelerate bitsandbytes"
    echo ""
    echo "2. Test your model:"
    echo "   python $LOCAL_DIR/deploy_${MODEL_NAME}.py"
    echo ""
    echo "3. Or use the detailed deployment guide:"
    echo "   See model_download_and_deployment.md"
    echo ""
    print_status "Your fine-tuned Code Llama model is ready to use!"
    
else
    print_error "Download failed!"
    exit 1
fi
