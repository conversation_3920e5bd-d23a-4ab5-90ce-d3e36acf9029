# Training Time and Resource Requirements

## ⏱️ Training Time Estimates (4-bit Quantization + LoRA)

### AWS g4dn.xlarge (1x NVIDIA T4, 16GB VRAM)

#### Dataset Size vs Training Time
| Dataset Size | Epochs | Estimated Time | Cost (Spot) | Cost (On-Demand) |
|-------------|--------|----------------|-------------|------------------|
| 10K samples | 3 | 2-4 hours | $0.32-$0.63 | $1.05-$2.10 |
| 50K samples | 3 | 6-10 hours | $0.95-$1.58 | $3.16-$5.26 |
| 100K samples | 3 | 12-18 hours | $1.90-$2.84 | $6.31-$9.47 |
| 200K samples | 3 | 20-30 hours | $3.16-$4.74 | $10.52-$15.78 |

#### Configuration Impact on Speed
| Configuration | Speed Multiplier | Memory Usage | Quality |
|--------------|------------------|--------------|---------|
| 4-bit + LoRA r=8 | 1.0x (baseline) | ~4GB | Good |
| 4-bit + LoRA r=16 | 0.9x | ~4.2GB | Better |
| 4-bit + LoRA r=32 | 0.8x | ~4.5GB | Best |
| 8-bit + LoRA r=16 | 0.7x | ~7GB | Better+ |
| Full fine-tuning | 0.1x | ~14GB | Best+ |

### Why 4-bit + LoRA is Optimal

#### Memory Efficiency
- **Original Model**: ~13-14GB RAM
- **4-bit Quantized**: ~3.5-4GB RAM (75% reduction)
- **LoRA Adapters**: +0.1-0.2GB
- **Total Training**: ~4-5GB VRAM needed

#### Speed Benefits
- **LoRA trains only ~1%** of model parameters
- **4-bit quantization** reduces memory bandwidth
- **Gradient computation** is much faster
- **Checkpoint saving** is quicker (smaller files)

#### Quality Retention
- **LoRA preserves** 95-98% of full fine-tuning quality
- **4-bit quantization** has minimal impact on performance
- **Perfect for code generation** tasks

## 💻 MacBook Pro M2 Hosting Requirements

### Your Setup: 16GB RAM M2 MacBook Pro

#### Memory Requirements
| Model Configuration | RAM Needed | Available for OS | Feasible? |
|-------------------|------------|------------------|-----------|
| Original 7B (FP16) | ~14GB | 2GB | ❌ Tight |
| 4-bit Quantized | ~4GB | 12GB | ✅ Perfect |
| 4-bit + LoRA | ~4.2GB | 11.8GB | ✅ Perfect |
| 8-bit Quantized | ~7GB | 9GB | ✅ Good |

#### Performance Expectations on M2
| Configuration | Tokens/Second | Response Time | Memory |
|--------------|---------------|---------------|---------|
| 4-bit Quantized | 8-12 tok/s | 5-8s for 50 tokens | ~4GB |
| 8-bit Quantized | 6-10 tok/s | 6-10s for 50 tokens | ~7GB |
| FP16 (if fits) | 4-8 tok/s | 8-12s for 50 tokens | ~14GB |

### Recommended M2 Setup

#### 1. Model Loading Script
```python
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel

def load_optimized_model(model_path, adapter_path=None):
    """Load 4-bit quantized model optimized for M2 MacBook"""
    
    # Load with 4-bit quantization
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        device_map="auto",
        torch_dtype=torch.float16,
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_use_double_quant=True,
        low_cpu_mem_usage=True
    )
    
    # Load LoRA adapter if available
    if adapter_path:
        model = PeftModel.from_pretrained(model, adapter_path)
    
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    return model, tokenizer

# Usage
model, tokenizer = load_optimized_model(
    "codellama/CodeLlama-7b-hf",
    "./models/codellama-7b-finetuned"  # Your trained adapter
)
```

#### 2. Memory Monitoring
```python
import psutil
import torch

def check_memory_usage():
    """Monitor memory usage on M2 MacBook"""
    
    # System memory
    memory = psutil.virtual_memory()
    print(f"System RAM: {memory.used/1e9:.1f}GB / {memory.total/1e9:.1f}GB")
    print(f"Available: {memory.available/1e9:.1f}GB")
    
    # GPU memory (if using MPS)
    if torch.backends.mps.is_available():
        print("MPS (Metal Performance Shaders) available")
    
    return memory.available > 2e9  # At least 2GB free

# Check before loading model
if check_memory_usage():
    model, tokenizer = load_optimized_model(...)
```

## 🚀 Optimization Tips

### For Training (AWS)
```yaml
# Optimal training config for g4dn.xlarge
training:
  per_device_train_batch_size: 4
  gradient_accumulation_steps: 4  # Effective batch size = 16
  fp16: true
  gradient_checkpointing: true
  dataloader_pin_memory: false
  
lora:
  r: 16  # Good balance of quality vs speed
  alpha: 32
  dropout: 0.1
```

### For Inference (M2 MacBook)
```python
# Optimal generation settings
generation_config = {
    "max_length": 512,  # Shorter responses = faster
    "do_sample": True,
    "temperature": 0.7,
    "top_p": 0.9,
    "pad_token_id": tokenizer.eos_token_id,
    "use_cache": True,  # Enable KV cache for speed
}

# Generate with memory management
with torch.no_grad():
    outputs = model.generate(
        input_ids,
        **generation_config
    )
```

## 📊 Real-World Examples

### Training Example (100K samples)
```bash
# Start training
python train_code_llama.py \
    --dataset_path ./dataset/processed \
    --output_dir ./models/codellama-7b-finetuned \
    --num_train_epochs 3 \
    --per_device_train_batch_size 4 \
    --learning_rate 2e-4

# Expected output:
# Training time: ~12-15 hours
# Cost (spot): ~$1.90-$2.37
# Final model size: ~4.2GB (with LoRA adapters)
```

### Inference Example (M2 MacBook)
```python
# Load your trained model
model, tokenizer = load_optimized_model(
    "codellama/CodeLlama-7b-hf",
    "./models/codellama-7b-finetuned"
)

# Generate code
prompt = "Write a Python function to sort a list:"
inputs = tokenizer(prompt, return_tensors="pt")

with torch.no_grad():
    outputs = model.generate(
        inputs.input_ids,
        max_length=200,
        temperature=0.7
    )

response = tokenizer.decode(outputs[0], skip_special_tokens=True)
print(response)

# Expected performance:
# Memory usage: ~4.2GB
# Generation speed: ~10 tokens/second
# Response time: ~5-8 seconds for 50 tokens
```

## ✅ Summary for Your Setup

### Training (AWS g4dn.xlarge)
- **Time**: 12-18 hours for 100K samples
- **Cost**: $1.90-$2.84 (spot pricing)
- **Memory**: ~4GB VRAM (fits comfortably)

### Inference (M2 MacBook 16GB)
- **Memory**: ~4.2GB RAM (leaves 11.8GB free)
- **Speed**: 8-12 tokens/second
- **Perfect fit** for your 16GB M2 MacBook!

Your 16GB M2 MacBook Pro is **perfectly suited** for running the 4-bit quantized + LoRA fine-tuned Code Llama 7B model with excellent performance and plenty of memory headroom.
