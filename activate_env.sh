#!/bin/bash
# Environment Activation Script
# Run this to activate the training environment

# Activate conda environment
source $(conda info --base)/etc/profile.d/conda.sh
conda activate llama-training

# Navigate to project directory
cd /data/codellama-training

# Display environment info
echo "=== Environment Activated ==="
echo "Current directory: $(pwd)"
echo "Python path: $(which python)"
echo "Python version: $(python --version)"

# Check CUDA availability
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
if python -c 'import torch; torch.cuda.is_available()' &> /dev/null; then
    echo "GPU: $(python -c 'import torch; print(torch.cuda.get_device_name(0))')"
    echo "GPU Memory: $(python -c 'import torch; print(f"{torch.cuda.get_device_properties(0).total_memory/1e9:.1f}GB")')"
fi

echo "=== Ready for training! ==="
